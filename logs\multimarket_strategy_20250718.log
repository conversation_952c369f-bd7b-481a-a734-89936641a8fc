root - INFO - ================================================================================
root - INFO - Starting new logging session
root - INFO - ================================================================================
fyers_config - INFO - ================================================================================
fyers_config - INFO - UNIFIED SCANNER APPLICATION STARTED
fyers_config - INFO - ================================================================================
fyers_config - INFO - Start time: 2025-07-18 00:03:46
config_loader - INFO - Configuration loaded successfully from C:\Users\<USER>\Desktop\Python\simple_hft\config.yaml
unified_scanner - INFO - Unified scanner initialized for market types: ['EQUITY', 'INDEX', 'FUTURES', 'OPTIONS']
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - UNIFIED SCANNER APPLICATION STARTED
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - Start time: 2025-07-18 00:03:46
unified_scanner - INFO - Enabled market types: EQUITY, INDEX, FUTURES, OPTIONS
unified_scanner - INFO - Validating prerequisites...
config_loader - INFO - Configuration validation successful
unified_scanner - INFO - All prerequisites satisfied
unified_scanner - INFO - Downloading latest symbol files...
unified_scanner - INFO - Symbol files downloaded successfully
unified_scanner - INFO - ============================================================
unified_scanner - INFO - STARTING EQUITY SCANNING
unified_scanner - INFO - ============================================================
universal_symbol_parser - INFO - Universal symbol parser initialized for 2 target symbols
universal_symbol_parser - INFO - Use all symbols: False
market_type_scanner - INFO - Initialized EQUITY scanner
market_type_scanner - INFO - Starting EQUITY scanning...
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - 
=== Fyers API Authentication ===
fyers_config - INFO - A browser window will open for you to log in to Fyers.
fyers_config - INFO - After logging in, you will be redirected to Google.
fyers_config - INFO - Copy the auth code from the URL and paste it here.
fyers_config - INFO - 
Please login in the private browser window that opened.
fyers_config - INFO - Authentication files saved to C:\Users\<USER>\Desktop\Python\simple_hft
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
pivot_point_integration - INFO - Pivot Point Integration initialized - Enabled: True, Type: DAILY
universal_symbol_parser - INFO - Loaded 2 symbols from NSE_CM.csv for market types: ['EQUITY']
universal_symbol_parser - INFO - Performance stats: Processed 8288 rows, found 2 valid symbols
universal_symbol_parser - INFO - Found 2 EQUITY symbols for scanning
market_type_scanner - INFO - Pivot point mode: Fetching market data for 2 EQUITY symbols
fyers_client - INFO - Batch 1/1 (2 symbols) - Rate: 0.0 batches/sec, ETA: 0s
fyers_client - INFO - Quote fetching completed: 2/2 symbols (100.0% batch success rate) in 0.2s
market_type_scanner - INFO - Volume filter: 2/2 symbols passed
market_type_scanner - INFO - LTP filter: 2/2 symbols passed
market_type_scanner - INFO - Applying pivot point calculations to 2 symbols
fyers_connect - INFO - Starting Fyers authentication process...
fyers_config - INFO - Fyers authentication successful!
fyers_connect - INFO - Successfully logged in to Fyers API
fyers_connect - INFO - Spot price for DIXON: 16107.0
fyers_connect - INFO - Spot price for MCX: 8259.5
market_type_scanner - INFO - Calculated pivot points for 2 symbols
pivot_point_integration - INFO - Filtered to top 2 symbols closest to minimum positive pivot points
market_type_scanner - INFO - EQUITY pivot point scanning completed: 2 symbols found
report_generator - INFO - Output directory ready: reports
report_generator - INFO - CSV report created successfully: reports\equity_scan_20250718_000402.csv
report_generator - INFO - Report contains 2 symbols
report_generator - INFO - Summary report created successfully: reports\equity_scan_summary_20250718_000402.txt
report_generator - INFO - Generated EQUITY reports:
report_generator - INFO -   CSV: reports\equity_scan_20250718_000402.csv
report_generator - INFO -   Summary: reports\equity_scan_summary_20250718_000402.txt
unified_scanner - INFO - ============================================================
unified_scanner - INFO - EQUITY SCANNING COMPLETED
unified_scanner - INFO - ============================================================
unified_scanner - INFO - EQUITY symbols found: 2
unified_scanner - INFO - EQUITY CSV Report: reports\equity_scan_20250718_000402.csv
unified_scanner - INFO - EQUITY Summary Report: reports\equity_scan_summary_20250718_000402.txt
unified_scanner - INFO - ============================================================
unified_scanner - INFO - STARTING INDEX SCANNING
unified_scanner - INFO - ============================================================
universal_symbol_parser - INFO - Universal symbol parser initialized for 2 target symbols
universal_symbol_parser - INFO - Use all symbols: False
market_type_scanner - INFO - Initialized INDEX scanner
market_type_scanner - INFO - Starting INDEX scanning...
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
pivot_point_integration - INFO - Pivot Point Integration initialized - Enabled: True, Type: DAILY
universal_symbol_parser - INFO - Loaded 0 symbols from NSE_CM.csv for market types: ['INDEX']
universal_symbol_parser - INFO - Performance stats: Processed 8288 rows, found 0 valid symbols
universal_symbol_parser - INFO - Found 0 INDEX symbols for scanning
market_type_scanner - WARNING - No INDEX symbols found for pivot point scanning
unified_scanner - WARNING - No INDEX symbols found matching the criteria
unified_scanner - INFO - ============================================================
unified_scanner - INFO - STARTING FUTURES SCANNING
unified_scanner - INFO - ============================================================
universal_symbol_parser - INFO - Universal symbol parser initialized for 2 target symbols
universal_symbol_parser - INFO - Use all symbols: False
market_type_scanner - INFO - Initialized FUTURES scanner
market_type_scanner - INFO - Starting FUTURES scanning...
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
pivot_point_integration - INFO - Pivot Point Integration initialized - Enabled: True, Type: DAILY
universal_symbol_parser - INFO - Loaded 6 symbols from NSE_FO.csv for market types: ['FUTURES']
universal_symbol_parser - INFO - Performance stats: Processed 78328 rows, found 6 valid symbols
universal_symbol_parser - INFO - Found 6 FUTURES symbols for scanning
market_type_scanner - INFO - Pivot point mode: Fetching market data for 6 FUTURES symbols
fyers_client - INFO - Batch 1/1 (6 symbols) - Rate: 0.0 batches/sec, ETA: 0s
fyers_client - INFO - Quote fetching completed: 6/6 symbols (100.0% batch success rate) in 0.2s
market_type_scanner - INFO - Volume filter: 6/6 symbols passed
market_type_scanner - INFO - LTP filter: 6/6 symbols passed
market_type_scanner - INFO - Applying pivot point calculations to 6 symbols
fyers_connect - INFO - Starting Fyers authentication process...
fyers_config - INFO - Fyers authentication successful!
fyers_connect - INFO - Successfully logged in to Fyers API
fyers_connect - INFO - Spot price for DIXON: 16107.0
fyers_connect - INFO - Spot price for MCX: 8259.5
market_type_scanner - INFO - Calculated pivot points for 6 symbols
pivot_point_integration - INFO - Filtered to top 6 symbols closest to minimum positive pivot points
market_type_scanner - INFO - FUTURES pivot point scanning completed: 6 symbols found
report_generator - INFO - Output directory ready: reports
report_generator - INFO - CSV report created successfully: reports\futures_scan_20250718_000405.csv
report_generator - INFO - Report contains 6 symbols
report_generator - INFO - Summary report created successfully: reports\futures_scan_summary_20250718_000405.txt
report_generator - INFO - Generated FUTURES reports:
report_generator - INFO -   CSV: reports\futures_scan_20250718_000405.csv
report_generator - INFO -   Summary: reports\futures_scan_summary_20250718_000405.txt
unified_scanner - INFO - ============================================================
unified_scanner - INFO - FUTURES SCANNING COMPLETED
unified_scanner - INFO - ============================================================
unified_scanner - INFO - FUTURES symbols found: 6
unified_scanner - INFO - FUTURES CSV Report: reports\futures_scan_20250718_000405.csv
unified_scanner - INFO - FUTURES Summary Report: reports\futures_scan_summary_20250718_000405.txt
unified_scanner - INFO - ============================================================
unified_scanner - INFO - STARTING OPTIONS SCANNING
unified_scanner - INFO - ============================================================
universal_symbol_parser - INFO - Universal symbol parser initialized for 2 target symbols
universal_symbol_parser - INFO - Use all symbols: False
market_type_scanner - INFO - Initialized OPTIONS scanner
options_chain_filter - INFO - Options chain filter initialized with strike level: 20
market_type_scanner - INFO - Starting OPTIONS scanning...
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
pivot_point_integration - INFO - Pivot Point Integration initialized - Enabled: True, Type: DAILY
fyers_connect - INFO - Starting Fyers authentication process...
fyers_config - INFO - Fyers authentication successful!
fyers_connect - INFO - Successfully logged in to Fyers API
fyers_connect - INFO - Spot price for DIXON: 16107.0
fyers_connect - INFO - Spot price for MCX: 8259.5
market_type_scanner - INFO - Pivot point mode: Processing symbols: ['DIXON', 'MCX']
root - INFO - Generating config for new symbol 'DIXON' based on spot price.
fyers_connect - ERROR - Monthly option symbol NSE:DIXON25JUL15100CE is invalid or returned no data. Tried symbol: NSE:DIXON25JUL15100CE for expiry_type: MONTHLY. If already using MONTHLY, this is likely a symbol or broker issue.
market_type_scanner - ERROR - Error processing symbol DIXON in pivot point mode: Monthly option symbol is invalid or returned no data. Tried symbol: NSE:DIXON25JUL15100CE for expiry_type: MONTHLY. If already using MONTHLY, this is likely a symbol or broker issue.
root - INFO - Generating config for new symbol 'MCX' based on spot price.
option_utils - WARNING - Could not find implied volatility for symbol=NSE:MCX25JUL7300CE, price=302.45, S=8259.5, K=7300.0, T=0.03561643835616438, r=0.065, option_type=CE: f(a) and f(b) must have different signs. Returning default IV.
market_type_scanner - INFO - Pivot point mode: Added 11 options for MCX
market_type_scanner - INFO - Pivot point mode: Total symbols selected: 11
market_type_scanner - INFO - Pivot point mode: Fetching market data for 11 OPTIONS symbols
fyers_client - INFO - Batch 1/1 (11 symbols) - Rate: 0.0 batches/sec, ETA: 0s
fyers_client - INFO - Quote fetching completed: 11/11 symbols (100.0% batch success rate) in 0.2s
market_type_scanner - INFO - Volume filter: 11/11 symbols passed
market_type_scanner - INFO - LTP filter: 11/11 symbols passed
market_type_scanner - INFO - Applying pivot point calculations to 11 symbols
fyers_connect - INFO - Starting Fyers authentication process...
fyers_config - INFO - Fyers authentication successful!
fyers_connect - INFO - Successfully logged in to Fyers API
fyers_connect - INFO - Spot price for DIXON: 16107.0
fyers_connect - INFO - Spot price for MCX: 8259.5
pivot_point_integration - INFO - Start Date and End Date:2025-07-16, End Date:2025-07-16
pivot_point_integration - INFO - Start Date and End Date:2025-07-16, End Date:2025-07-16
pivot_point_integration - INFO - Start Date and End Date:2025-07-16, End Date:2025-07-16
pivot_point_integration - INFO - Start Date and End Date:2025-07-16, End Date:2025-07-16
pivot_point_integration - INFO - Start Date and End Date:2025-07-16, End Date:2025-07-16
pivot_point_integration - INFO - Start Date and End Date:2025-07-16, End Date:2025-07-16
pivot_point_integration - INFO - Start Date and End Date:2025-07-16, End Date:2025-07-16
pivot_point_integration - INFO - Start Date and End Date:2025-07-16, End Date:2025-07-16
pivot_point_integration - INFO - Start Date and End Date:2025-07-16, End Date:2025-07-16
pivot_point_integration - INFO - Start Date and End Date:2025-07-16, End Date:2025-07-16
pivot_point_integration - INFO - Start Date and End Date:2025-07-16, End Date:2025-07-16
market_type_scanner - INFO - Calculated pivot points for 11 symbols
pivot_point_integration - INFO - Filtered to top 10 symbols closest to minimum positive pivot points
market_type_scanner - INFO - OPTIONS pivot point scanning completed: 10 symbols found
report_generator - INFO - Output directory ready: reports
report_generator - WARNING - Found 10 unpaired symbols
report_generator - INFO - Sorted 10 symbols by underlying, month, and preserved CE/PE pairing
report_generator - INFO - Preserved 0 CE/PE pairs, 10 unpaired symbols
options_chain_filter - INFO - Options chain filter initialized with strike level: 20
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
options_chain_filter - INFO - Fetching spot prices for 1 underlyings...
fyers_client - INFO - Batch 1/1 (1 symbols) - Rate: 0.0 batches/sec, ETA: 0s
fyers_client - INFO - Quote fetching completed: 1/1 symbols (100.0% batch success rate) in 0.3s
options_chain_filter - INFO - Retrieved spot prices for 1/1 underlyings
report_generator - INFO - Fetched spot prices for 1 underlyings for CSV report
report_generator - INFO - CSV report created successfully: reports\options_scan_20250718_000409.csv
report_generator - INFO - Report contains 10 symbols
report_generator - WARNING - Found 10 unpaired symbols
report_generator - INFO - Sorted 10 symbols by underlying, month, and preserved CE/PE pairing
report_generator - INFO - Preserved 0 CE/PE pairs, 10 unpaired symbols
report_generator - INFO - Summary report created successfully: reports\options_scan_summary_20250718_000409.txt
report_generator - INFO - Generated OPTIONS reports:
report_generator - INFO -   CSV: reports\options_scan_20250718_000409.csv
report_generator - INFO -   Summary: reports\options_scan_summary_20250718_000409.txt
unified_scanner - INFO - ============================================================
unified_scanner - INFO - OPTIONS SCANNING COMPLETED
unified_scanner - INFO - ============================================================
unified_scanner - INFO - OPTIONS symbols found: 10
unified_scanner - INFO - OPTIONS CSV Report: reports\options_scan_20250718_000409.csv
unified_scanner - INFO - OPTIONS Summary Report: reports\options_scan_summary_20250718_000409.txt
unified_scanner - INFO - Combined summary report generated: reports\combined_scan_summary_20250718_000410.txt
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - UNIFIED SCANNER COMPLETED SUCCESSFULLY
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - End time: 2025-07-18 00:04:10
unified_scanner - INFO - Total symbols found: 18
unified_scanner - INFO -   - EQUITY symbols: 2
unified_scanner - INFO -   - INDEX symbols: 0
unified_scanner - INFO -   - FUTURES symbols: 6
unified_scanner - INFO -   - OPTIONS symbols: 10
unified_scanner - INFO - Combined summary: reports\combined_scan_summary_20250718_000410.txt
root - INFO - ================================================================================
root - INFO - Starting new logging session
root - INFO - ================================================================================
fyers_config - INFO - ================================================================================
fyers_config - INFO - UNIFIED SCANNER APPLICATION STARTED
fyers_config - INFO - ================================================================================
fyers_config - INFO - Start time: 2025-07-18 00:06:57
config_loader - INFO - Configuration loaded successfully from C:\Users\<USER>\Desktop\Python\simple_hft\config.yaml
unified_scanner - INFO - Unified scanner initialized for market types: ['EQUITY', 'INDEX', 'FUTURES', 'OPTIONS']
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - UNIFIED SCANNER APPLICATION STARTED
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - Start time: 2025-07-18 00:06:57
unified_scanner - INFO - Enabled market types: EQUITY, INDEX, FUTURES, OPTIONS
unified_scanner - INFO - Validating prerequisites...
config_loader - INFO - Configuration validation successful
unified_scanner - INFO - All prerequisites satisfied
unified_scanner - INFO - Downloading latest symbol files...
unified_scanner - INFO - Symbol files downloaded successfully
unified_scanner - INFO - ============================================================
unified_scanner - INFO - STARTING EQUITY SCANNING
unified_scanner - INFO - ============================================================
universal_symbol_parser - INFO - Universal symbol parser initialized for 2 target symbols
universal_symbol_parser - INFO - Use all symbols: False
market_type_scanner - INFO - Initialized EQUITY scanner
market_type_scanner - INFO - Starting EQUITY scanning...
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
pivot_point_integration - INFO - Pivot Point Integration initialized - Enabled: True, Type: DAILY
universal_symbol_parser - INFO - Loaded 2 symbols from NSE_CM.csv for market types: ['EQUITY']
universal_symbol_parser - INFO - Performance stats: Processed 8288 rows, found 2 valid symbols
universal_symbol_parser - INFO - Found 2 EQUITY symbols for scanning
market_type_scanner - INFO - Pivot point mode: Fetching market data for 2 EQUITY symbols
fyers_client - INFO - Batch 1/1 (2 symbols) - Rate: 0.0 batches/sec, ETA: 0s
fyers_client - INFO - Quote fetching completed: 2/2 symbols (100.0% batch success rate) in 0.3s
market_type_scanner - INFO - Volume filter: 2/2 symbols passed
market_type_scanner - INFO - LTP filter: 2/2 symbols passed
market_type_scanner - INFO - Applying pivot point calculations to 2 symbols
fyers_connect - INFO - Starting Fyers authentication process...
fyers_config - INFO - Fyers authentication successful!
fyers_connect - INFO - Successfully logged in to Fyers API
fyers_connect - INFO - Spot price for DIXON: 16107.0
fyers_connect - INFO - Spot price for MCX: 8259.5
market_type_scanner - INFO - Calculated pivot points for 2 symbols
pivot_point_integration - INFO - Filtered to top 2 symbols closest to minimum positive pivot points
market_type_scanner - INFO - EQUITY pivot point scanning completed: 2 symbols found
report_generator - INFO - Output directory ready: reports
report_generator - INFO - CSV report created successfully: reports\equity_scan_20250718_000700.csv
report_generator - INFO - Report contains 2 symbols
report_generator - INFO - Summary report created successfully: reports\equity_scan_summary_20250718_000700.txt
report_generator - INFO - Generated EQUITY reports:
report_generator - INFO -   CSV: reports\equity_scan_20250718_000700.csv
report_generator - INFO -   Summary: reports\equity_scan_summary_20250718_000700.txt
unified_scanner - INFO - ============================================================
unified_scanner - INFO - EQUITY SCANNING COMPLETED
unified_scanner - INFO - ============================================================
unified_scanner - INFO - EQUITY symbols found: 2
unified_scanner - INFO - EQUITY CSV Report: reports\equity_scan_20250718_000700.csv
unified_scanner - INFO - EQUITY Summary Report: reports\equity_scan_summary_20250718_000700.txt
unified_scanner - INFO - ============================================================
unified_scanner - INFO - STARTING INDEX SCANNING
unified_scanner - INFO - ============================================================
universal_symbol_parser - INFO - Universal symbol parser initialized for 2 target symbols
universal_symbol_parser - INFO - Use all symbols: False
market_type_scanner - INFO - Initialized INDEX scanner
market_type_scanner - INFO - Starting INDEX scanning...
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
pivot_point_integration - INFO - Pivot Point Integration initialized - Enabled: True, Type: DAILY
universal_symbol_parser - INFO - Loaded 0 symbols from NSE_CM.csv for market types: ['INDEX']
universal_symbol_parser - INFO - Performance stats: Processed 8288 rows, found 0 valid symbols
universal_symbol_parser - INFO - Found 0 INDEX symbols for scanning
market_type_scanner - WARNING - No INDEX symbols found for pivot point scanning
unified_scanner - WARNING - No INDEX symbols found matching the criteria
unified_scanner - INFO - ============================================================
unified_scanner - INFO - STARTING FUTURES SCANNING
unified_scanner - INFO - ============================================================
universal_symbol_parser - INFO - Universal symbol parser initialized for 2 target symbols
universal_symbol_parser - INFO - Use all symbols: False
market_type_scanner - INFO - Initialized FUTURES scanner
market_type_scanner - INFO - Starting FUTURES scanning...
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
pivot_point_integration - INFO - Pivot Point Integration initialized - Enabled: True, Type: DAILY
universal_symbol_parser - INFO - Loaded 6 symbols from NSE_FO.csv for market types: ['FUTURES']
universal_symbol_parser - INFO - Performance stats: Processed 78328 rows, found 6 valid symbols
universal_symbol_parser - INFO - Found 6 FUTURES symbols for scanning
market_type_scanner - INFO - Pivot point mode: Fetching market data for 6 FUTURES symbols
fyers_client - INFO - Batch 1/1 (6 symbols) - Rate: 0.0 batches/sec, ETA: 0s
fyers_client - INFO - Quote fetching completed: 6/6 symbols (100.0% batch success rate) in 0.2s
market_type_scanner - INFO - Volume filter: 6/6 symbols passed
market_type_scanner - INFO - LTP filter: 6/6 symbols passed
market_type_scanner - INFO - Applying pivot point calculations to 6 symbols
fyers_connect - INFO - Starting Fyers authentication process...
fyers_config - INFO - Fyers authentication successful!
fyers_connect - INFO - Successfully logged in to Fyers API
fyers_connect - INFO - Spot price for DIXON: 16107.0
fyers_connect - INFO - Spot price for MCX: 8259.5
market_type_scanner - INFO - Calculated pivot points for 6 symbols
pivot_point_integration - INFO - Filtered to top 6 symbols closest to minimum positive pivot points
market_type_scanner - INFO - FUTURES pivot point scanning completed: 6 symbols found
report_generator - INFO - Output directory ready: reports
report_generator - INFO - CSV report created successfully: reports\futures_scan_20250718_000703.csv
report_generator - INFO - Report contains 6 symbols
report_generator - INFO - Summary report created successfully: reports\futures_scan_summary_20250718_000703.txt
report_generator - INFO - Generated FUTURES reports:
report_generator - INFO -   CSV: reports\futures_scan_20250718_000703.csv
report_generator - INFO -   Summary: reports\futures_scan_summary_20250718_000703.txt
unified_scanner - INFO - ============================================================
unified_scanner - INFO - FUTURES SCANNING COMPLETED
unified_scanner - INFO - ============================================================
unified_scanner - INFO - FUTURES symbols found: 6
unified_scanner - INFO - FUTURES CSV Report: reports\futures_scan_20250718_000703.csv
unified_scanner - INFO - FUTURES Summary Report: reports\futures_scan_summary_20250718_000703.txt
unified_scanner - INFO - ============================================================
unified_scanner - INFO - STARTING OPTIONS SCANNING
unified_scanner - INFO - ============================================================
universal_symbol_parser - INFO - Universal symbol parser initialized for 2 target symbols
universal_symbol_parser - INFO - Use all symbols: False
market_type_scanner - INFO - Initialized OPTIONS scanner
options_chain_filter - INFO - Options chain filter initialized with strike level: 20
market_type_scanner - INFO - Starting OPTIONS scanning...
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
pivot_point_integration - INFO - Pivot Point Integration initialized - Enabled: True, Type: DAILY
fyers_connect - INFO - Starting Fyers authentication process...
fyers_config - INFO - Fyers authentication successful!
fyers_connect - INFO - Successfully logged in to Fyers API
fyers_connect - INFO - Spot price for DIXON: 16107.0
fyers_connect - INFO - Spot price for MCX: 8259.5
market_type_scanner - INFO - Pivot point mode: Processing symbols: ['DIXON', 'MCX']
root - INFO - Generating config for new symbol 'DIXON' based on spot price.
fyers_connect - ERROR - Monthly option symbol NSE:DIXON25JUL15100CE is invalid or returned no data. Tried symbol: NSE:DIXON25JUL15100CE for expiry_type: MONTHLY. If already using MONTHLY, this is likely a symbol or broker issue.
market_type_scanner - ERROR - Error processing symbol DIXON in pivot point mode: Monthly option symbol is invalid or returned no data. Tried symbol: NSE:DIXON25JUL15100CE for expiry_type: MONTHLY. If already using MONTHLY, this is likely a symbol or broker issue.
root - INFO - Generating config for new symbol 'MCX' based on spot price.
option_utils - WARNING - Could not find implied volatility for symbol=NSE:MCX25JUL7300CE, price=302.45, S=8259.5, K=7300.0, T=0.03561643835616438, r=0.065, option_type=CE: f(a) and f(b) must have different signs. Returning default IV.
market_type_scanner - INFO - Pivot point mode: Added 11 options for MCX
market_type_scanner - INFO - Pivot point mode: Total symbols selected: 11
market_type_scanner - INFO - Pivot point mode: Fetching market data for 11 OPTIONS symbols
fyers_client - INFO - Batch 1/1 (11 symbols) - Rate: 0.0 batches/sec, ETA: 0s
fyers_client - INFO - Quote fetching completed: 11/11 symbols (100.0% batch success rate) in 0.3s
market_type_scanner - INFO - Volume filter: 11/11 symbols passed
market_type_scanner - INFO - LTP filter: 11/11 symbols passed
market_type_scanner - INFO - Applying pivot point calculations to 11 symbols
fyers_connect - INFO - Starting Fyers authentication process...
fyers_config - INFO - Fyers authentication successful!
fyers_connect - INFO - Successfully logged in to Fyers API
fyers_connect - INFO - Spot price for DIXON: 16107.0
fyers_connect - INFO - Spot price for MCX: 8259.5
market_type_scanner - INFO - Calculated pivot points for 11 symbols
pivot_point_integration - INFO - Filtered to top 10 symbols closest to minimum positive pivot points
market_type_scanner - INFO - OPTIONS pivot point scanning completed: 10 symbols found
report_generator - INFO - Output directory ready: reports
report_generator - WARNING - Found 10 unpaired symbols
report_generator - INFO - Sorted 10 symbols by underlying, month, and preserved CE/PE pairing
report_generator - INFO - Preserved 0 CE/PE pairs, 10 unpaired symbols
options_chain_filter - INFO - Options chain filter initialized with strike level: 20
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
options_chain_filter - INFO - Fetching spot prices for 1 underlyings...
fyers_client - INFO - Batch 1/1 (1 symbols) - Rate: 0.0 batches/sec, ETA: 0s
fyers_client - INFO - Quote fetching completed: 1/1 symbols (100.0% batch success rate) in 0.2s
options_chain_filter - INFO - Retrieved spot prices for 1/1 underlyings
report_generator - INFO - Fetched spot prices for 1 underlyings for CSV report
report_generator - INFO - CSV report created successfully: reports\options_scan_20250718_000710.csv
report_generator - INFO - Report contains 10 symbols
report_generator - WARNING - Found 10 unpaired symbols
report_generator - INFO - Sorted 10 symbols by underlying, month, and preserved CE/PE pairing
report_generator - INFO - Preserved 0 CE/PE pairs, 10 unpaired symbols
report_generator - INFO - Summary report created successfully: reports\options_scan_summary_20250718_000710.txt
report_generator - INFO - Generated OPTIONS reports:
report_generator - INFO -   CSV: reports\options_scan_20250718_000710.csv
report_generator - INFO -   Summary: reports\options_scan_summary_20250718_000710.txt
unified_scanner - INFO - ============================================================
unified_scanner - INFO - OPTIONS SCANNING COMPLETED
unified_scanner - INFO - ============================================================
unified_scanner - INFO - OPTIONS symbols found: 10
unified_scanner - INFO - OPTIONS CSV Report: reports\options_scan_20250718_000710.csv
unified_scanner - INFO - OPTIONS Summary Report: reports\options_scan_summary_20250718_000710.txt
unified_scanner - INFO - Combined summary report generated: reports\combined_scan_summary_20250718_000710.txt
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - UNIFIED SCANNER COMPLETED SUCCESSFULLY
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - End time: 2025-07-18 00:07:10
unified_scanner - INFO - Total symbols found: 18
unified_scanner - INFO -   - EQUITY symbols: 2
unified_scanner - INFO -   - INDEX symbols: 0
unified_scanner - INFO -   - FUTURES symbols: 6
unified_scanner - INFO -   - OPTIONS symbols: 10
unified_scanner - INFO - Combined summary: reports\combined_scan_summary_20250718_000710.txt
root - INFO - ================================================================================
root - INFO - Starting new logging session
root - INFO - ================================================================================
fyers_config - INFO - ================================================================================
fyers_config - INFO - UNIFIED SCANNER APPLICATION STARTED
fyers_config - INFO - ================================================================================
fyers_config - INFO - Start time: 2025-07-18 00:10:53
config_loader - INFO - Configuration loaded successfully from C:\Users\<USER>\Desktop\Python\simple_hft\config.yaml
unified_scanner - INFO - Unified scanner initialized for market types: ['EQUITY', 'INDEX', 'FUTURES', 'OPTIONS']
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - UNIFIED SCANNER APPLICATION STARTED
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - Start time: 2025-07-18 00:10:53
unified_scanner - INFO - Enabled market types: EQUITY, INDEX, FUTURES, OPTIONS
unified_scanner - INFO - Validating prerequisites...
config_loader - INFO - Configuration validation successful
unified_scanner - INFO - All prerequisites satisfied
unified_scanner - INFO - Downloading latest symbol files...
unified_scanner - INFO - Symbol files downloaded successfully
unified_scanner - INFO - ============================================================
unified_scanner - INFO - STARTING EQUITY SCANNING
unified_scanner - INFO - ============================================================
universal_symbol_parser - INFO - Universal symbol parser initialized for 2 target symbols
universal_symbol_parser - INFO - Use all symbols: False
market_type_scanner - INFO - Initialized EQUITY scanner
market_type_scanner - INFO - Starting EQUITY scanning...
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
pivot_point_integration - INFO - Pivot Point Integration initialized - Enabled: True, Type: DAILY
universal_symbol_parser - INFO - Loaded 2 symbols from NSE_CM.csv for market types: ['EQUITY']
universal_symbol_parser - INFO - Performance stats: Processed 8288 rows, found 2 valid symbols
universal_symbol_parser - INFO - Found 2 EQUITY symbols for scanning
market_type_scanner - INFO - Pivot point mode: Fetching market data for 2 EQUITY symbols
fyers_client - INFO - Batch 1/1 (2 symbols) - Rate: 0.0 batches/sec, ETA: 0s
fyers_client - INFO - Quote fetching completed: 2/2 symbols (100.0% batch success rate) in 0.3s
market_type_scanner - INFO - Volume filter: 2/2 symbols passed
market_type_scanner - INFO - LTP filter: 2/2 symbols passed
market_type_scanner - INFO - Applying pivot point calculations to 2 symbols
fyers_connect - INFO - Starting Fyers authentication process...
fyers_config - INFO - Fyers authentication successful!
fyers_connect - INFO - Successfully logged in to Fyers API
fyers_connect - INFO - Spot price for DIXON: 16107.0
fyers_connect - INFO - Spot price for MCX: 8259.5
market_type_scanner - INFO - Calculated pivot points for 2 symbols
pivot_point_integration - INFO - Filtered to top 2 symbols closest to minimum positive pivot points
market_type_scanner - INFO - EQUITY pivot point scanning completed: 2 symbols found
report_generator - INFO - Output directory ready: reports
report_generator - INFO - CSV report created successfully: reports\equity_scan_20250718_001056.csv
report_generator - INFO - Report contains 2 symbols
report_generator - INFO - Summary report created successfully: reports\equity_scan_summary_20250718_001056.txt
report_generator - INFO - Generated EQUITY reports:
report_generator - INFO -   CSV: reports\equity_scan_20250718_001056.csv
report_generator - INFO -   Summary: reports\equity_scan_summary_20250718_001056.txt
unified_scanner - INFO - ============================================================
unified_scanner - INFO - EQUITY SCANNING COMPLETED
unified_scanner - INFO - ============================================================
unified_scanner - INFO - EQUITY symbols found: 2
unified_scanner - INFO - EQUITY CSV Report: reports\equity_scan_20250718_001056.csv
unified_scanner - INFO - EQUITY Summary Report: reports\equity_scan_summary_20250718_001056.txt
unified_scanner - INFO - ============================================================
unified_scanner - INFO - STARTING INDEX SCANNING
unified_scanner - INFO - ============================================================
universal_symbol_parser - INFO - Universal symbol parser initialized for 2 target symbols
universal_symbol_parser - INFO - Use all symbols: False
market_type_scanner - INFO - Initialized INDEX scanner
market_type_scanner - INFO - Starting INDEX scanning...
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
pivot_point_integration - INFO - Pivot Point Integration initialized - Enabled: True, Type: DAILY
universal_symbol_parser - INFO - Loaded 0 symbols from NSE_CM.csv for market types: ['INDEX']
universal_symbol_parser - INFO - Performance stats: Processed 8288 rows, found 0 valid symbols
universal_symbol_parser - INFO - Found 0 INDEX symbols for scanning
market_type_scanner - WARNING - No INDEX symbols found for pivot point scanning
unified_scanner - WARNING - No INDEX symbols found matching the criteria
unified_scanner - INFO - ============================================================
unified_scanner - INFO - STARTING FUTURES SCANNING
unified_scanner - INFO - ============================================================
universal_symbol_parser - INFO - Universal symbol parser initialized for 2 target symbols
universal_symbol_parser - INFO - Use all symbols: False
market_type_scanner - INFO - Initialized FUTURES scanner
market_type_scanner - INFO - Starting FUTURES scanning...
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
pivot_point_integration - INFO - Pivot Point Integration initialized - Enabled: True, Type: DAILY
universal_symbol_parser - INFO - Loaded 6 symbols from NSE_FO.csv for market types: ['FUTURES']
universal_symbol_parser - INFO - Performance stats: Processed 78328 rows, found 6 valid symbols
universal_symbol_parser - INFO - Found 6 FUTURES symbols for scanning
market_type_scanner - INFO - Pivot point mode: Fetching market data for 6 FUTURES symbols
fyers_client - INFO - Batch 1/1 (6 symbols) - Rate: 0.0 batches/sec, ETA: 0s
fyers_client - INFO - Quote fetching completed: 6/6 symbols (100.0% batch success rate) in 0.2s
market_type_scanner - INFO - Volume filter: 6/6 symbols passed
market_type_scanner - INFO - LTP filter: 6/6 symbols passed
market_type_scanner - INFO - Applying pivot point calculations to 6 symbols
fyers_connect - INFO - Starting Fyers authentication process...
fyers_config - INFO - Fyers authentication successful!
fyers_connect - INFO - Successfully logged in to Fyers API
fyers_connect - INFO - Spot price for DIXON: 16107.0
fyers_connect - INFO - Spot price for MCX: 8259.5
market_type_scanner - INFO - Calculated pivot points for 6 symbols
pivot_point_integration - INFO - Filtered to top 6 symbols closest to minimum positive pivot points
market_type_scanner - INFO - FUTURES pivot point scanning completed: 6 symbols found
report_generator - INFO - Output directory ready: reports
report_generator - INFO - CSV report created successfully: reports\futures_scan_20250718_001059.csv
report_generator - INFO - Report contains 6 symbols
report_generator - INFO - Summary report created successfully: reports\futures_scan_summary_20250718_001059.txt
report_generator - INFO - Generated FUTURES reports:
report_generator - INFO -   CSV: reports\futures_scan_20250718_001059.csv
report_generator - INFO -   Summary: reports\futures_scan_summary_20250718_001059.txt
unified_scanner - INFO - ============================================================
unified_scanner - INFO - FUTURES SCANNING COMPLETED
unified_scanner - INFO - ============================================================
unified_scanner - INFO - FUTURES symbols found: 6
unified_scanner - INFO - FUTURES CSV Report: reports\futures_scan_20250718_001059.csv
unified_scanner - INFO - FUTURES Summary Report: reports\futures_scan_summary_20250718_001059.txt
unified_scanner - INFO - ============================================================
unified_scanner - INFO - STARTING OPTIONS SCANNING
unified_scanner - INFO - ============================================================
universal_symbol_parser - INFO - Universal symbol parser initialized for 2 target symbols
universal_symbol_parser - INFO - Use all symbols: False
market_type_scanner - INFO - Initialized OPTIONS scanner
options_chain_filter - INFO - Options chain filter initialized with strike level: 20
market_type_scanner - INFO - Starting OPTIONS scanning...
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
pivot_point_integration - INFO - Pivot Point Integration initialized - Enabled: True, Type: DAILY
fyers_connect - INFO - Starting Fyers authentication process...
fyers_config - INFO - Fyers authentication successful!
fyers_connect - INFO - Successfully logged in to Fyers API
fyers_connect - INFO - Spot price for DIXON: 16107.0
fyers_connect - INFO - Spot price for MCX: 8259.5
market_type_scanner - INFO - Pivot point mode: Processing symbols: ['DIXON', 'MCX']
root - INFO - Generating config for new symbol 'DIXON' based on spot price.
fyers_connect - ERROR - Monthly option symbol NSE:DIXON25JUL15100CE is invalid or returned no data. Tried symbol: NSE:DIXON25JUL15100CE for expiry_type: MONTHLY. If already using MONTHLY, this is likely a symbol or broker issue.
market_type_scanner - ERROR - Error processing symbol DIXON in pivot point mode: Monthly option symbol is invalid or returned no data. Tried symbol: NSE:DIXON25JUL15100CE for expiry_type: MONTHLY. If already using MONTHLY, this is likely a symbol or broker issue.
root - INFO - Generating config for new symbol 'MCX' based on spot price.
option_utils - WARNING - Could not find implied volatility for symbol=NSE:MCX25JUL7300CE, price=302.45, S=8259.5, K=7300.0, T=0.03561643835616438, r=0.065, option_type=CE: f(a) and f(b) must have different signs. Returning default IV.
market_type_scanner - INFO - Pivot point mode: Added 11 options for MCX
market_type_scanner - INFO - Pivot point mode: Total symbols selected: 11
market_type_scanner - INFO - Pivot point mode: Fetching market data for 11 OPTIONS symbols
fyers_client - INFO - Batch 1/1 (11 symbols) - Rate: 0.0 batches/sec, ETA: 0s
fyers_client - INFO - Quote fetching completed: 11/11 symbols (100.0% batch success rate) in 0.2s
market_type_scanner - INFO - Volume filter: 11/11 symbols passed
market_type_scanner - INFO - LTP filter: 11/11 symbols passed
market_type_scanner - INFO - Applying pivot point calculations to 11 symbols
fyers_connect - INFO - Starting Fyers authentication process...
fyers_config - INFO - Fyers authentication successful!
fyers_connect - INFO - Successfully logged in to Fyers API
fyers_connect - INFO - Spot price for DIXON: 16107.0
fyers_connect - INFO - Spot price for MCX: 8259.5
market_type_scanner - INFO - Calculated pivot points for 11 symbols
pivot_point_integration - INFO - Filtered to top 10 symbols closest to minimum positive pivot points
market_type_scanner - INFO - OPTIONS pivot point scanning completed: 10 symbols found
report_generator - INFO - Output directory ready: reports
report_generator - WARNING - Found 10 unpaired symbols
report_generator - INFO - Sorted 10 symbols by underlying, month, and preserved CE/PE pairing
report_generator - INFO - Preserved 0 CE/PE pairs, 10 unpaired symbols
options_chain_filter - INFO - Options chain filter initialized with strike level: 20
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
options_chain_filter - INFO - Fetching spot prices for 1 underlyings...
fyers_client - INFO - Batch 1/1 (1 symbols) - Rate: 0.0 batches/sec, ETA: 0s
fyers_client - INFO - Quote fetching completed: 1/1 symbols (100.0% batch success rate) in 0.4s
options_chain_filter - INFO - Retrieved spot prices for 1/1 underlyings
report_generator - INFO - Fetched spot prices for 1 underlyings for CSV report
report_generator - INFO - CSV report created successfully: reports\options_scan_20250718_001106.csv
report_generator - INFO - Report contains 10 symbols
report_generator - WARNING - Found 10 unpaired symbols
report_generator - INFO - Sorted 10 symbols by underlying, month, and preserved CE/PE pairing
report_generator - INFO - Preserved 0 CE/PE pairs, 10 unpaired symbols
report_generator - INFO - Summary report created successfully: reports\options_scan_summary_20250718_001106.txt
report_generator - INFO - Generated OPTIONS reports:
report_generator - INFO -   CSV: reports\options_scan_20250718_001106.csv
report_generator - INFO -   Summary: reports\options_scan_summary_20250718_001106.txt
unified_scanner - INFO - ============================================================
unified_scanner - INFO - OPTIONS SCANNING COMPLETED
unified_scanner - INFO - ============================================================
unified_scanner - INFO - OPTIONS symbols found: 10
unified_scanner - INFO - OPTIONS CSV Report: reports\options_scan_20250718_001106.csv
unified_scanner - INFO - OPTIONS Summary Report: reports\options_scan_summary_20250718_001106.txt
unified_scanner - INFO - Combined summary report generated: reports\combined_scan_summary_20250718_001106.txt
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - UNIFIED SCANNER COMPLETED SUCCESSFULLY
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - End time: 2025-07-18 00:11:06
unified_scanner - INFO - Total symbols found: 18
unified_scanner - INFO -   - EQUITY symbols: 2
unified_scanner - INFO -   - INDEX symbols: 0
unified_scanner - INFO -   - FUTURES symbols: 6
unified_scanner - INFO -   - OPTIONS symbols: 10
unified_scanner - INFO - Combined summary: reports\combined_scan_summary_20250718_001106.txt
root - INFO - ================================================================================
root - INFO - Starting new logging session
root - INFO - ================================================================================
fyers_config - INFO - ================================================================================
fyers_config - INFO - UNIFIED SCANNER APPLICATION STARTED
fyers_config - INFO - ================================================================================
fyers_config - INFO - Start time: 2025-07-18 00:22:54
config_loader - INFO - Configuration loaded successfully from C:\Users\<USER>\Desktop\Python\simple_hft\config.yaml
unified_scanner - INFO - Unified scanner initialized for market types: ['EQUITY', 'INDEX', 'FUTURES', 'OPTIONS']
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - UNIFIED SCANNER APPLICATION STARTED
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - Start time: 2025-07-18 00:22:54
unified_scanner - INFO - Enabled market types: EQUITY, INDEX, FUTURES, OPTIONS
unified_scanner - INFO - Validating prerequisites...
config_loader - INFO - Configuration validation successful
unified_scanner - INFO - All prerequisites satisfied
unified_scanner - INFO - Downloading latest symbol files...
unified_scanner - INFO - Symbol files downloaded successfully
unified_scanner - INFO - ============================================================
unified_scanner - INFO - STARTING EQUITY SCANNING
unified_scanner - INFO - ============================================================
universal_symbol_parser - INFO - Universal symbol parser initialized for 1 target symbols
universal_symbol_parser - INFO - Use all symbols: True
market_type_scanner - INFO - Initialized EQUITY scanner
market_type_scanner - INFO - Starting EQUITY scanning...
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
pivot_point_integration - INFO - Pivot Point Integration initialized - Enabled: False, Type: DAILY
market_type_scanner - INFO - Starting EQUITY scanning...
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
pivot_point_integration - INFO - Pivot Point Integration initialized - Enabled: False, Type: DAILY
universal_symbol_parser - INFO - Loaded 2040 symbols from NSE_CM.csv for market types: ['EQUITY']
universal_symbol_parser - INFO - Performance stats: Processed 8288 rows, found 2040 valid symbols
universal_symbol_parser - INFO - Found 2040 EQUITY symbols for scanning
market_type_scanner - INFO - Fetching market data for 2040 EQUITY symbols
fyers_client - INFO - Batch 1/41 (50 symbols) - Rate: 0.0 batches/sec, ETA: 0s
fyers_client - INFO - Batch 10/41 (50 symbols) - Rate: 3.8 batches/sec, ETA: 8s
fyers_client - INFO - Batch 26/41 (50 symbols) - Rate: 3.9 batches/sec, ETA: 4s
fyers_client - INFO - Batch 41/41 (40 symbols) - Rate: 3.3 batches/sec, ETA: 0s
fyers_client - INFO - Quote fetching completed: 2040/2040 symbols (100.0% batch success rate) in 12.5s
fyers_client - INFO - FyersClient: Fetching historical data for NSE:ABAN-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:ABAN-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:ARE&M-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:ARE&M-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:EMAMIPAP-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:EMAMIPAP-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:FACT-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:FACT-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:GODREJCP-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:GODREJCP-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:SCHAEFFLER-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:SCHAEFFLER-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:ORICONENT-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:ORICONENT-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:SETFNIF50-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:SETFNIF50-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:BALKRISHNA-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:BALKRISHNA-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:ADANIENSOL-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:ADANIENSOL-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:MANORAMA-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:MANORAMA-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:FEDERALBNK-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:FEDERALBNK-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:CONFIPET-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:CONFIPET-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:SYNGENE-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:SYNGENE-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:NGLFINE-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:NGLFINE-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:AYMSYNTEX-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:AYMSYNTEX-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:PPL-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:PPL-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:AARON-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:AARON-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:RAMASTEEL-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:RAMASTEEL-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:AURUM-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:AURUM-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:RADHIKAJWE-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:RADHIKAJWE-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:CSLFINANCE-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:CSLFINANCE-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:FINCABLES-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:FINCABLES-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:JISLJALEQS-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:JISLJALEQS-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:SHAREINDIA-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:SHAREINDIA-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:STEELCITY-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:STEELCITY-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:KSHITIJPOL-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:KSHITIJPOL-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:FINPIPE-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:FINPIPE-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:MUTHOOTCAP-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:MUTHOOTCAP-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:DYCL-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:DYCL-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:AXISBNKETF-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 79 OHLC data points for NSE:AXISBNKETF-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:LUPIN-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:LUPIN-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:UNITDSPR-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:UNITDSPR-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:SATIN-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:SATIN-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:ARVSMART-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:ARVSMART-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:POWERMECH-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:POWERMECH-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:RAMRAT-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:RAMRAT-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:MOHEALTH-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:MOHEALTH-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:UTINIFTETF-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:UTINIFTETF-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:CONS-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:CONS-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:UTISENSETF-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:UTISENSETF-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:UFLEX-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:UFLEX-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:SHARDAMOTR-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:SHARDAMOTR-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:NAVKARCORP-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:NAVKARCORP-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:NIFTYBEES-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:NIFTYBEES-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:KRBL-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:KRBL-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:MPSLTD-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:MPSLTD-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:MARKSANS-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:MARKSANS-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:TCI-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:TCI-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:MOM30IETF-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:MOM30IETF-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:SHREEPUSHK-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:SHREEPUSHK-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:GUJGASLTD-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:GUJGASLTD-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:BHARTIARTL-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:BHARTIARTL-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:SADBHIN-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:SADBHIN-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:HDFCNEXT50-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:HDFCNEXT50-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:HDFCNIF100-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:HDFCNIF100-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:ASAL-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:ASAL-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:OLECTRA-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:OLECTRA-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:PONNIERODE-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:PONNIERODE-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:PNB-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:PNB-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:MNC-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:MNC-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:NIFTYQLITY-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:NIFTYQLITY-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:MOMENTUM-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:MOMENTUM-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:SURANAT&P-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:SURANAT&P-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:TIMESGTY-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:TIMESGTY-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:INFRAIETF-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:INFRAIETF-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:INDIAMART-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:INDIAMART-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:FOSECOIND-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:FOSECOIND-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:SPORTKING-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:SPORTKING-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:OFSS-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:OFSS-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:UNIONBANK-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:UNIONBANK-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:ZENSARTECH-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:ZENSARTECH-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:SILVERADD-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:SILVERADD-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:OAL-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:OAL-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:NUCLEUS-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:NUCLEUS-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:SYRMA-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:SYRMA-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:CANBK-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:CANBK-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:MOQUALITY-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 83 OHLC data points for NSE:MOQUALITY-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:MOVALUE-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:MOVALUE-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:GABRIEL-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:GABRIEL-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:GVPTECH-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:GVPTECH-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:GRMOVER-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:GRMOVER-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:HDFCSILVER-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:HDFCSILVER-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:DBOL-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:DBOL-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:GENESYS-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:GENESYS-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:OMAXAUTO-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:OMAXAUTO-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:GODREJIND-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:GODREJIND-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:RADAAN-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:RADAAN-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:JUNIORBEES-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:JUNIORBEES-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:DIVISLAB-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:DIVISLAB-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:TMB-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:TMB-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:RADICO-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:RADICO-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:MARUTI-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:MARUTI-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:GARFIBRES-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:GARFIBRES-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:LIQUIDBEES-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:LIQUIDBEES-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:INFOBEAN-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:INFOBEAN-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:SANGINITA-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:SANGINITA-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:BANKIETF-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:BANKIETF-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:DCMNVL-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:DCMNVL-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:SATIA-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:SATIA-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:SIRCA-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:SIRCA-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:SECURKLOUD-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:SECURKLOUD-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:KSOLVES-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:KSOLVES-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:AGRITECH-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:AGRITECH-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:LIBAS-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:LIBAS-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:SUMIT-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:SUMIT-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:SHRENIK-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:SHRENIK-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:CREATIVE-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:CREATIVE-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:HARSHA-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:HARSHA-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:COFFEEDAY-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:COFFEEDAY-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:IDFCFIRSTB-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:IDFCFIRSTB-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:AXISILVER-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:AXISILVER-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:INDIGO-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:INDIGO-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:SHK-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:SHK-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:VARDHACRLC-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:VARDHACRLC-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:UCOBANK-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:UCOBANK-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:BAGFILMS-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:BAGFILMS-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:JSL-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:JSL-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:NSIL-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:NSIL-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:HDFCGROWTH-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:HDFCGROWTH-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:WELSPUNLIV-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:WELSPUNLIV-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:HDFCQUAL-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:HDFCQUAL-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:JAIBALAJI-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:JAIBALAJI-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:HDFCVALUE-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:HDFCVALUE-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:IGL-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:IGL-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:GHCL-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:GHCL-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:DREDGECORP-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:DREDGECORP-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:TVTODAY-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:TVTODAY-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:UPL-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:UPL-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:UNIENTER-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:UNIENTER-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:SIL-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:SIL-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:LUXIND-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:LUXIND-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:BALAXI-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:BALAXI-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:LICNETFN50-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 83 OHLC data points for NSE:LICNETFN50-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:MUKANDLTD-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:MUKANDLTD-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:APOLLO-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:APOLLO-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:AFFLE-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:AFFLE-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:PETRONET-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:PETRONET-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:PTC-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:PTC-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:DHANBANK-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:DHANBANK-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:VAIBHAVGBL-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:VAIBHAVGBL-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:TTKHLTCARE-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:TTKHLTCARE-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:BIOCON-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:BIOCON-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:TIPSFILMS-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:TIPSFILMS-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:MAHABANK-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:MAHABANK-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:PVTBANIETF-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:PVTBANIETF-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:GICHSGFIN-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:GICHSGFIN-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:ZOTA-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:ZOTA-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:SALZERELEC-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:SALZERELEC-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:POONAWALLA-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:POONAWALLA-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:XPROINDIA-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:XPROINDIA-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:RKFORGE-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:RKFORGE-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:DATAMATICS-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:DATAMATICS-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:KRITINUT-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:KRITINUT-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:SPANDANA-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:SPANDANA-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:BANKBEES-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:BANKBEES-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:LICNETFSEN-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 69 OHLC data points for NSE:LICNETFSEN-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:PILANIINVS-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:PILANIINVS-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:GIPCL-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:GIPCL-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:CCL-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:CCL-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:NV20-EQ with interval 60 (mapped: 60), days: 15
fyers_client - WARNING - Rate limit hit for NSE:NV20-EQ, attempt 1/5. Retrying after 3.0 seconds...
fyers_client - INFO - FyersClient: Fetching historical data for NSE:NV20-EQ with interval 60 (mapped: 60), days: 15
fyers_client - WARNING - Rate limit hit for NSE:NV20-EQ, attempt 2/5. Retrying after 3.0 seconds...
fyers_client - INFO - FyersClient: Fetching historical data for NSE:NV20-EQ with interval 60 (mapped: 60), days: 15
fyers_client - WARNING - Rate limit hit for NSE:NV20-EQ, attempt 3/5. Retrying after 3.0 seconds...
fyers_client - INFO - FyersClient: Fetching historical data for NSE:NV20-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:NV20-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:EXCELINDUS-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:EXCELINDUS-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:LT-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:LT-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:APARINDS-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:APARINDS-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:AMBICAAGAR-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:AMBICAAGAR-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:CENTEXT-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:CENTEXT-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:NIITLTD-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:NIITLTD-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:GLAXO-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:GLAXO-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:EMIL-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:EMIL-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:ULTRACEMCO-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:ULTRACEMCO-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:TCS-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:TCS-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:HDFCMOMENT-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:HDFCMOMENT-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:COFORGE-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:COFORGE-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:HDFCLOWVOL-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:HDFCLOWVOL-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:TEXINFRA-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:TEXINFRA-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:DCI-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:DCI-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:PPLPHARMA-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:PPLPHARMA-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:FORCEMOT-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:FORCEMOT-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:WESTLIFE-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:WESTLIFE-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:GULFPETRO-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:GULFPETRO-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:TRACXN-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:TRACXN-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:HDFCNIFTY-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:HDFCNIFTY-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:HDFCSENSEX-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:HDFCSENSEX-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:LINCOLN-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:LINCOLN-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:INDSWFTLTD-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:INDSWFTLTD-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:GUFICBIO-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:GUFICBIO-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:ASTRAMICRO-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:ASTRAMICRO-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:GTL-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:GTL-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:WELENT-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:WELENT-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:NARMADA-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:NARMADA-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:NTPC-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:NTPC-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:SALSTEEL-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:SALSTEEL-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:NEWGEN-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:NEWGEN-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:BOHRAIND-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:BOHRAIND-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:KOTARISUG-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:KOTARISUG-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:LALPATHLAB-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:LALPATHLAB-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:JBMA-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:JBMA-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:DWARKESH-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:DWARKESH-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:INDOCO-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:INDOCO-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:GMBREW-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:GMBREW-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:SUPRAJIT-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:SUPRAJIT-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:PATELENG-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:PATELENG-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:ALKEM-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:ALKEM-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:XCHANGING-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:XCHANGING-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:JSWSTEEL-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:JSWSTEEL-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:CONSOFINVT-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:CONSOFINVT-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:GNFC-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:GNFC-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:JPPOWER-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:JPPOWER-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:GOKEX-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:GOKEX-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:ACEINTEG-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:ACEINTEG-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:EVEREADY-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:EVEREADY-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:GOYALALUM-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:GOYALALUM-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:SAKSOFT-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:SAKSOFT-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:HPIL-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:HPIL-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:ALLDIGI-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:ALLDIGI-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:SUKHJITS-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:SUKHJITS-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:IIFL-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:IIFL-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:GODFRYPHLP-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:GODFRYPHLP-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:SHOPERSTOP-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:SHOPERSTOP-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:MANGALAM-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:MANGALAM-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:WELCORP-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:WELCORP-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:SSWL-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:SSWL-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:NH-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:NH-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:CYBERMEDIA-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:CYBERMEDIA-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:AMBER-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:AMBER-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:SURYALAXMI-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:SURYALAXMI-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:SHAHALLOYS-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:SHAHALLOYS-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:GLAND-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:GLAND-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:JKPAPER-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:JKPAPER-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:PRIMESECU-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:PRIMESECU-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:63MOONS-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:63MOONS-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:GRANULES-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:GRANULES-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:MANINDS-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:MANINDS-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:VHL-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:VHL-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:DCXINDIA-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:DCXINDIA-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:GEOJITFSL-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:GEOJITFSL-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:FMGOETZE-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:FMGOETZE-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:GENUSPOWER-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:GENUSPOWER-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:SWELECTES-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:SWELECTES-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:YESBANK-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:YESBANK-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:MSPL-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:MSPL-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:FUSION-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:FUSION-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:MCLEODRUSS-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:MCLEODRUSS-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:MEDANTA-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:MEDANTA-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:KANSAINER-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:KANSAINER-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:BIKAJI-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:BIKAJI-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:GOLDIAM-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:GOLDIAM-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:RML-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:RML-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:SASKEN-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:SASKEN-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:ICIL-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:ICIL-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:FCSSOFT-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:FCSSOFT-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:ESG-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:ESG-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:FAZE3Q-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:FAZE3Q-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:IMPAL-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:IMPAL-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:AEROENTER-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:AEROENTER-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:SUZLON-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:SUZLON-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:AURIONPRO-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:AURIONPRO-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:ACI-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:ACI-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:RENUKA-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:RENUKA-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:FIVESTAR-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:FIVESTAR-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:BASML-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:BASML-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:SCPL-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:SCPL-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:KAYNES-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:KAYNES-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:HDFCNIFIT-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:HDFCNIFIT-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:HDFCPVTBAN-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:HDFCPVTBAN-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:DOLATALGO-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:DOLATALGO-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:BBTCL-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:BBTCL-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:HNDFDS-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:HNDFDS-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:RUSTOMJEE-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:RUSTOMJEE-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:GRASIM-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:GRASIM-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:GREAVESCOT-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:GREAVESCOT-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:ZIMLAB-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:ZIMLAB-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:GSFC-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:GSFC-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:SWSOLAR-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:SWSOLAR-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:CASTROLIND-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:CASTROLIND-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:AAATECH-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:AAATECH-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:FINIETF-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:FINIETF-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:PRECAM-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:PRECAM-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:GUJALKALI-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:GUJALKALI-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:MBAPL-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:MBAPL-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:AMBUJACEM-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:AMBUJACEM-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:TEAMLEASE-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:TEAMLEASE-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:DJML-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:DJML-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:ROUTE-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:ROUTE-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:KRISHANA-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:KRISHANA-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:GFLLIMITED-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:GFLLIMITED-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:SARVESHWAR-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:SARVESHWAR-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:NPBET-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 76 OHLC data points for NSE:NPBET-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:ABB-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:ABB-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:SAFARI-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:SAFARI-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:UNIPARTS-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:UNIPARTS-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:360ONE-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:360ONE-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:IIFLCAPS-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:IIFLCAPS-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:TRIVENI-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:TRIVENI-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:SILVER1-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:SILVER1-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:AIAENG-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:AIAENG-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:EKC-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:EKC-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:VIMTALABS-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:VIMTALABS-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:ORIENTCER-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:ORIENTCER-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:QUICKHEAL-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:QUICKHEAL-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:REPRO-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:REPRO-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:HARRMALAYA-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:HARRMALAYA-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:EBBETF0433-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:EBBETF0433-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:GSEC10IETF-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 82 OHLC data points for NSE:GSEC10IETF-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:PVRINOX-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:PVRINOX-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:GALAXYSURF-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:GALAXYSURF-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:CELEBRITY-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:CELEBRITY-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:NITINSPIN-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:NITINSPIN-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:ROHLTD-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:ROHLTD-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:ENIL-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:ENIL-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:GSPL-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:GSPL-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:COMMOIETF-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:COMMOIETF-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:JAGRAN-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:JAGRAN-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:SULA-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:SULA-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:SADBHAV-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:SADBHAV-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:KEC-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:KEC-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:JKCEMENT-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:JKCEMENT-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:INDOTECH-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:INDOTECH-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:LANDMARK-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:LANDMARK-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:M&MFIN-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:M&MFIN-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:AFSL-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:AFSL-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:BLKASHYAP-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:BLKASHYAP-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:NITCO-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:NITCO-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:CENTURYPLY-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:CENTURYPLY-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:KEI-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:KEI-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:HDFCBANK-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:HDFCBANK-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:SOLARINDS-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:SOLARINDS-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:GALLANTT-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:GALLANTT-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:MALUPAPER-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:MALUPAPER-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:KFINTECH-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:KFINTECH-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:HEG-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:HEG-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:UTTAMSUGAR-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:UTTAMSUGAR-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:SDBL-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:SDBL-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:KKCL-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:KKCL-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:BHAGERIA-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:BHAGERIA-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:SUNTV-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:SUNTV-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:GPIL-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:GPIL-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:RSYSTEMS-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:RSYSTEMS-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:EMKAY-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:EMKAY-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:KELLTONTEC-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:KELLTONTEC-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:LOKESHMACH-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:LOKESHMACH-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:BALPHARMA-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:BALPHARMA-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:RATNAMANI-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:RATNAMANI-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:KAMDHENU-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:KAMDHENU-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:PIONEEREMB-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:PIONEEREMB-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:HEROMOTOCO-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:HEROMOTOCO-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:JKLAKSHMI-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:JKLAKSHMI-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:PFOCUS-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:PFOCUS-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:ALLCARGO-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:ALLCARGO-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:AKASH-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:AKASH-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:MUNJALAU-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:MUNJALAU-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:EMAMILTD-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:EMAMILTD-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:NECCLTD-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:NECCLTD-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:GMRAIRPORT-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:GMRAIRPORT-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:VIDHIING-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:VIDHIING-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:TECHM-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:TECHM-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:AVONMORE-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:AVONMORE-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:GRINDWELL-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:GRINDWELL-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:VOLTAMP-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:VOLTAMP-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:RADIANTCMS-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:RADIANTCMS-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:ATLANTAA-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:ATLANTAA-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:ACE-EQ with interval 60 (mapped: 60), days: 15
fyers_client - WARNING - Rate limit hit for NSE:ACE-EQ, attempt 1/5. Retrying after 3.0 seconds...
fyers_client - INFO - FyersClient: Fetching historical data for NSE:ACE-EQ with interval 60 (mapped: 60), days: 15
fyers_client - WARNING - Rate limit hit for NSE:ACE-EQ, attempt 2/5. Retrying after 3.0 seconds...
fyers_client - INFO - FyersClient: Fetching historical data for NSE:ACE-EQ with interval 60 (mapped: 60), days: 15
fyers_client - WARNING - Rate limit hit for NSE:ACE-EQ, attempt 3/5. Retrying after 3.0 seconds...
fyers_client - INFO - FyersClient: Fetching historical data for NSE:ACE-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:ACE-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:SELAN-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:SELAN-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:ANDHRSUGAR-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:ANDHRSUGAR-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:HIMATSEIDE-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:HIMATSEIDE-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:IRCTC-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:IRCTC-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:ANANTRAJ-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:ANANTRAJ-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:HINDALCO-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:HINDALCO-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:SKYGOLD-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:SKYGOLD-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:JMFINANCIL-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:JMFINANCIL-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:ELECON-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:ELECON-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:BANKETFADD-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:BANKETFADD-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:TALBROAUTO-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:TALBROAUTO-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:ALICON-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:ALICON-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:GEECEE-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:GEECEE-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:ACLGATI-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:ACLGATI-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:VISHWARAJ-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:VISHWARAJ-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:FIEMIND-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:FIEMIND-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:HINDCOMPOS-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:HINDCOMPOS-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:JHS-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:JHS-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:DCBBANK-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:DCBBANK-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:GLOBALVECT-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:GLOBALVECT-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:GTLINFRA-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:GTLINFRA-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:FLUOROCHEM-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:FLUOROCHEM-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:NAUKRI-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:NAUKRI-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:MANOMAY-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:MANOMAY-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:SHIVAMAUTO-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:SHIVAMAUTO-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:BBL-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:BBL-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:GESHIP-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:GESHIP-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:TORNTPOWER-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:TORNTPOWER-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:BAIDFIN-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:BAIDFIN-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:MANALIPETC-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:MANALIPETC-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:SUTLEJTEX-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:SUTLEJTEX-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:GREENPANEL-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:GREENPANEL-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:LTFOODS-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:LTFOODS-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:RUCHIRA-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:RUCHIRA-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:SOBHA-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:SOBHA-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:DONEAR-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:DONEAR-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:MMFL-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:MMFL-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:AARTIPHARM-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:AARTIPHARM-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:KUANTUM-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:KUANTUM-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:JINDRILL-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:JINDRILL-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:BANCOINDIA-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:BANCOINDIA-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:HIRECT-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:HIRECT-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:CREST-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:CREST-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:PLASTIBLEN-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:PLASTIBLEN-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:NFL-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:NFL-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:HINDUNILVR-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:HINDUNILVR-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:PRAENG-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:PRAENG-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:LIQUID1-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:LIQUID1-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:PARACABLES-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:PARACABLES-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:HBLENGINE-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:HBLENGINE-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:TANLA-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:TANLA-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:JASH-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:JASH-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:ABSLBANETF-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:ABSLBANETF-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:ZEEMEDIA-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:ZEEMEDIA-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:AVTNPL-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:AVTNPL-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:LUMAXTECH-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:LUMAXTECH-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:VEEDOL-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:VEEDOL-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:HINDOILEXP-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:HINDOILEXP-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:SANGHVIMOV-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:SANGHVIMOV-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:HINDPETRO-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:HINDPETRO-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:ASMS-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:ASMS-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:SAGCEM-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:SAGCEM-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:IGPL-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:IGPL-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:SIYSIL-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:SIYSIL-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:PTL-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:PTL-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:AUTOIND-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:AUTOIND-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:NETWORK18-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:NETWORK18-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:GANDHITUBE-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:GANDHITUBE-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:AGI-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:AGI-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:PITTIENG-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:PITTIENG-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:BTML-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:BTML-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:UNOMINDA-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:UNOMINDA-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:IZMO-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:IZMO-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:NSLNISP-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:NSLNISP-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:NINSYS-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:NINSYS-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:TIMKEN-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:TIMKEN-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:HUBTOWN-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:HUBTOWN-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:TIIL-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:TIIL-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:HDFCBSE500-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:HDFCBSE500-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:HDFCSML250-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:HDFCSML250-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:HDFCMID150-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:HDFCMID150-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:HINDZINC-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:HINDZINC-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:TVSSRICHAK-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:TVSSRICHAK-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:REDINGTON-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:REDINGTON-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:PGIL-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:PGIL-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:ORIENTBELL-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:ORIENTBELL-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:GOLDETF-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:GOLDETF-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:SEQUENT-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:SEQUENT-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:PFC-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:PFC-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:FSL-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:FSL-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:OSWALSEEDS-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:OSWALSEEDS-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:INDIANB-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:INDIANB-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:SMSPHARMA-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:SMSPHARMA-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:HSCL-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:HSCL-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:GANESHHOUC-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:GANESHHOUC-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:APOLLOPIPE-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:APOLLOPIPE-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:IDEA-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:IDEA-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:POCL-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:POCL-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:AMDIND-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:AMDIND-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:GODREJAGRO-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:GODREJAGRO-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:PAGEIND-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:PAGEIND-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:ASTRAL-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:ASTRAL-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:SANOFI-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:SANOFI-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:LAOPALA-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:LAOPALA-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:GOLDBEES-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:GOLDBEES-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:JKTYRE-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:JKTYRE-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:NAHARSPING-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:NAHARSPING-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:EMBDL-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:EMBDL-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:LEXUS-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:LEXUS-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:DIVGIITTS-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:DIVGIITTS-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:HLVLTD-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:HLVLTD-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:GOKULAGRO-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:GOKULAGRO-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:THEMISMED-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:THEMISMED-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:NCLIND-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:NCLIND-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:BALAMINES-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:BALAMINES-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:BANSWRAS-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:BANSWRAS-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:ABSLLIQUID-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 72 OHLC data points for NSE:ABSLLIQUID-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:ICRA-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:ICRA-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:GOLDSHARE-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:GOLDSHARE-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:NDLVENTURE-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:NDLVENTURE-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:BIRLANU-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:BIRLANU-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:PHOENIXLTD-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:PHOENIXLTD-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:AROGRANITE-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:AROGRANITE-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:BFUTILITIE-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:BFUTILITIE-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:SRHHYPOLTD-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:SRHHYPOLTD-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:PSUBNKIETF-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:PSUBNKIETF-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:FORTIS-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:FORTIS-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:ALPHAGEO-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:ALPHAGEO-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:GSLSU-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:GSLSU-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:WEBELSOLAR-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:WEBELSOLAR-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:TGBHOTELS-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:TGBHOTELS-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:ACCURACY-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:ACCURACY-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:INSECTICID-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:INSECTICID-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:KMSUGAR-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:KMSUGAR-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:AKZOINDIA-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:AKZOINDIA-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:NAVINFLUOR-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:NAVINFLUOR-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:GUJAPOLLO-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:GUJAPOLLO-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:GOACARBON-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:GOACARBON-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:NAGREEKEXP-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:NAGREEKEXP-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:TIMETECHNO-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:TIMETECHNO-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:HGS-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:HGS-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:DLF-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:DLF-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:AXSENSEX-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:AXSENSEX-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:ADVANIHOTR-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:ADVANIHOTR-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:IDBI-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:IDBI-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:NELCAST-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:NELCAST-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:V2RETAIL-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:V2RETAIL-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:TARMAT-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:TARMAT-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:LOWVOL-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 82 OHLC data points for NSE:LOWVOL-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:SPARC-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:SPARC-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:REPL-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:REPL-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:ADSL-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:ADSL-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:DECCANCE-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:DECCANCE-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:VIPCLOTHNG-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:VIPCLOTHNG-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:ALPA-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:ALPA-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:IFBIND-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:IFBIND-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:OMAXE-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:OMAXE-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:GOLD1-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:GOLD1-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:USK-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:USK-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:CENTRALBK-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:CENTRALBK-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:SAHYADRI-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:SAHYADRI-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:KDDL-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:KDDL-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:IFCI-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:IFCI-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:KPRMILL-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:KPRMILL-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:PURVA-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:PURVA-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:CIEINDIA-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:CIEINDIA-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:GSEC10YEAR-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:GSEC10YEAR-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:RBA-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:RBA-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:NAGREEKCAP-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 83 OHLC data points for NSE:NAGREEKCAP-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:MOTILALOFS-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:MOTILALOFS-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:INDOWIND-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:INDOWIND-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:MAGNUM-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:MAGNUM-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:CSBBANK-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:CSBBANK-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:SHIVAMILLS-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:SHIVAMILLS-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:KSCL-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:KSCL-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:POWERGRID-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:POWERGRID-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:CENTUM-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:CENTUM-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:CCCL-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:CCCL-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:MANGCHEFER-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:MANGCHEFER-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:PSUBNKBEES-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:PSUBNKBEES-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:BAJAJELEC-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:BAJAJELEC-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:CERA-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:CERA-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:DELTACORP-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:DELTACORP-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:AVALON-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:AVALON-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:PSUBANK-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:PSUBANK-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:BLAL-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:BLAL-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:RELIGARE-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:RELIGARE-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:MOGSEC-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:MOGSEC-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:BVCL-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:BVCL-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:ASTERDM-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:ASTERDM-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:ADANIPORTS-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:ADANIPORTS-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:SILVERETF-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:SILVERETF-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:EDELWEISS-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:EDELWEISS-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:INDHOTEL-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:INDHOTEL-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:KOLTEPATIL-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:KOLTEPATIL-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:RGL-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:RGL-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:KAUSHALYA-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:KAUSHALYA-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:COLPAL-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:COLPAL-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:JYOTHYLAB-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:JYOTHYLAB-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:INDIACEM-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:INDIACEM-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:MADHAV-EQ with interval 60 (mapped: 60), days: 15
fyers_client - WARNING - Rate limit hit for NSE:MADHAV-EQ, attempt 1/5. Retrying after 3.0 seconds...
fyers_client - INFO - FyersClient: Fetching historical data for NSE:MADHAV-EQ with interval 60 (mapped: 60), days: 15
fyers_client - WARNING - Rate limit hit for NSE:MADHAV-EQ, attempt 2/5. Retrying after 3.0 seconds...
fyers_client - INFO - FyersClient: Fetching historical data for NSE:MADHAV-EQ with interval 60 (mapped: 60), days: 15
fyers_client - WARNING - Rate limit hit for NSE:MADHAV-EQ, attempt 3/5. Retrying after 3.0 seconds...
fyers_client - INFO - FyersClient: Fetching historical data for NSE:MADHAV-EQ with interval 60 (mapped: 60), days: 15
fyers_client - WARNING - Rate limit hit for NSE:MADHAV-EQ, attempt 4/5. Retrying after 3.0 seconds...
fyers_client - INFO - FyersClient: Fetching historical data for NSE:MADHAV-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:MADHAV-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:KALYANIFRG-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:KALYANIFRG-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:RHL-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:RHL-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:HITECHCORP-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:HITECHCORP-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:TARIL-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:TARIL-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:ECLERX-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:ECLERX-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:KIRLPNU-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:KIRLPNU-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:BRIGADE-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:BRIGADE-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:MANAKSIA-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:MANAKSIA-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:ARIES-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:ARIES-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:INDIAGLYCO-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:INDIAGLYCO-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:DVL-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:DVL-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:PATINTLOG-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:PATINTLOG-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:UJJIVANSFB-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:UJJIVANSFB-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:LICNFNHGP-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 81 OHLC data points for NSE:LICNFNHGP-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:AMBIKCO-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:AMBIKCO-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:BIRLAMONEY-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:BIRLAMONEY-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:CEATLTD-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:CEATLTD-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:JKIL-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:JKIL-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:CORDSCABLE-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:CORDSCABLE-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:ONMOBILE-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:ONMOBILE-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:KNRCON-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:KNRCON-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:VIRINCHI-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:VIRINCHI-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:INDIANHUME-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:INDIANHUME-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:BANG-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:BANG-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:SEPC-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:SEPC-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:IRB-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:IRB-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:QGOLDHALF-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:QGOLDHALF-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:NMDC-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:NMDC-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:RAIN-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:RAIN-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:SHALPAINTS-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:SHALPAINTS-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:GSS-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:GSS-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:RECLTD-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:RECLTD-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:GOLDETFADD-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:GOLDETFADD-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:VGUARD-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:VGUARD-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:PDMJEPAPER-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:PDMJEPAPER-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:MANKIND-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:MANKIND-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:DHANI-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:DHANI-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:RACE-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:RACE-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:EIHAHOTELS-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:EIHAHOTELS-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:APCOTEXIND-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:APCOTEXIND-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:ATAM-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:ATAM-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:NESCO-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:NESCO-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:TITAGARH-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:TITAGARH-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:DPSCLTD-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:DPSCLTD-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:ACL-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:ACL-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:RKEC-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:RKEC-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:HCG-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:HCG-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:AVG-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:AVG-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:RVHL-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:RVHL-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:APOLLOHOSP-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:APOLLOHOSP-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:GMMPFAUDLR-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:GMMPFAUDLR-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:GILLETTE-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:GILLETTE-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:TARC-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:TARC-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:LATTEYS-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:LATTEYS-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:HINDWAREAP-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:HINDWAREAP-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:MGEL-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:MGEL-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:INFY-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:INFY-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:INGERRAND-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:INGERRAND-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:PRINCEPIPE-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:PRINCEPIPE-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:BHARATWIRE-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:BHARATWIRE-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:FAIRCHEMOR-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:FAIRCHEMOR-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:PAVNAIND-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:PAVNAIND-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:TATAINVEST-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:TATAINVEST-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:IOC-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:IOC-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:INFIBEAM-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:INFIBEAM-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:EBBETF0430-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:EBBETF0430-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:LINDEINDIA-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:LINDEINDIA-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:BECTORFOOD-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:BECTORFOOD-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:APOLLOTYRE-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:APOLLOTYRE-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:IONEXCHANG-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:IONEXCHANG-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:IPCALAB-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:IPCALAB-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:MMP-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:MMP-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:VAISHALI-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:VAISHALI-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:ANDHRAPAP-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:ANDHRAPAP-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:ITC-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:ITC-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:SUNDROP-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:SUNDROP-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:KIRIINDUS-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:KIRIINDUS-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:ANIKINDS-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:ANIKINDS-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:BAJAJFINSV-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:BAJAJFINSV-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:PTCIL-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:PTCIL-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:DIGISPICE-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:DIGISPICE-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:GHCLTEXTIL-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:GHCLTEXTIL-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:GOKUL-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:GOKUL-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:UBL-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:UBL-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:ALMONDZ-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:ALMONDZ-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:HGINFRA-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:HGINFRA-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:RPGLIFE-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:RPGLIFE-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:ITI-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:ITI-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:SILVRETF-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:SILVRETF-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:GVT&D-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:GVT&D-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:ARCHIDPLY-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:ARCHIDPLY-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:LOTUSEYE-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:LOTUSEYE-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:IVP-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:IVP-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:QNIFTY-EQ with interval 60 (mapped: 60), days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:QNIFTY-EQ
fyers_client - INFO - FyersClient: Fetching historical data for NSE:IKIO-EQ with interval 60 (mapped: 60), days: 15
unified_scanner - INFO - Application interrupted by user
root - INFO - ================================================================================
root - INFO - Starting new logging session
root - INFO - ================================================================================
fyers_config - INFO - ================================================================================
fyers_config - INFO - UNIFIED SCANNER APPLICATION STARTED
fyers_config - INFO - ================================================================================
fyers_config - INFO - Start time: 2025-07-18 00:40:25
config_loader - INFO - Configuration loaded successfully from C:\Users\<USER>\Desktop\Python\simple_hft\config.yaml
unified_scanner - INFO - Unified scanner initialized for market types: ['EQUITY', 'INDEX', 'FUTURES', 'OPTIONS']
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - UNIFIED SCANNER APPLICATION STARTED
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - Start time: 2025-07-18 00:40:25
unified_scanner - INFO - Enabled market types: EQUITY, INDEX, FUTURES, OPTIONS
unified_scanner - INFO - Validating prerequisites...
config_loader - INFO - Configuration validation successful
unified_scanner - INFO - All prerequisites satisfied
unified_scanner - INFO - Downloading latest symbol files...
unified_scanner - INFO - Symbol files downloaded successfully
unified_scanner - INFO - ============================================================
unified_scanner - INFO - STARTING EQUITY SCANNING
unified_scanner - INFO - ============================================================
universal_symbol_parser - INFO - Universal symbol parser initialized for 2 target symbols
universal_symbol_parser - INFO - Use all symbols: False
market_type_scanner - INFO - Initialized EQUITY scanner
market_type_scanner - INFO - Starting EQUITY scanning...
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
pivot_point_integration - INFO - Pivot Point Integration initialized - Enabled: True, Type: DAILY
universal_symbol_parser - INFO - Loaded 2 symbols from NSE_CM.csv for market types: ['EQUITY']
universal_symbol_parser - INFO - Performance stats: Processed 8288 rows, found 2 valid symbols
universal_symbol_parser - INFO - Found 2 EQUITY symbols for scanning
market_type_scanner - INFO - Pivot point mode: Fetching market data for 2 EQUITY symbols
fyers_client - INFO - Batch 1/1 (2 symbols) - Rate: 0.0 batches/sec, ETA: 0s
fyers_client - INFO - Quote fetching completed: 2/2 symbols (100.0% batch success rate) in 0.2s
market_type_scanner - INFO - Volume filter: 2/2 symbols passed
market_type_scanner - INFO - LTP filter: 2/2 symbols passed
market_type_scanner - INFO - Applying pivot point calculations to 2 symbols
fyers_connect - INFO - Starting Fyers authentication process...
fyers_config - INFO - Fyers authentication successful!
fyers_connect - INFO - Successfully logged in to Fyers API
fyers_connect - INFO - Spot price for DIXON: 16107.0
fyers_connect - INFO - Spot price for MCX: 8259.5
market_type_scanner - INFO - Calculated pivot points for 2 symbols
pivot_point_integration - INFO - Filtered to top 2 symbols closest to minimum positive pivot points
market_type_scanner - INFO - EQUITY pivot point scanning completed: 2 symbols found
report_generator - INFO - Output directory ready: reports
report_generator - INFO - CSV report created successfully: reports\equity_scan_20250718_004028.csv
report_generator - INFO - Report contains 2 symbols
report_generator - INFO - Summary report created successfully: reports\equity_scan_summary_20250718_004028.txt
report_generator - INFO - Generated EQUITY reports:
report_generator - INFO -   CSV: reports\equity_scan_20250718_004028.csv
report_generator - INFO -   Summary: reports\equity_scan_summary_20250718_004028.txt
unified_scanner - INFO - ============================================================
unified_scanner - INFO - EQUITY SCANNING COMPLETED
unified_scanner - INFO - ============================================================
unified_scanner - INFO - EQUITY symbols found: 2
unified_scanner - INFO - EQUITY CSV Report: reports\equity_scan_20250718_004028.csv
unified_scanner - INFO - EQUITY Summary Report: reports\equity_scan_summary_20250718_004028.txt
unified_scanner - INFO - ============================================================
unified_scanner - INFO - STARTING INDEX SCANNING
unified_scanner - INFO - ============================================================
universal_symbol_parser - INFO - Universal symbol parser initialized for 2 target symbols
universal_symbol_parser - INFO - Use all symbols: False
market_type_scanner - INFO - Initialized INDEX scanner
market_type_scanner - INFO - Starting INDEX scanning...
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
pivot_point_integration - INFO - Pivot Point Integration initialized - Enabled: True, Type: DAILY
universal_symbol_parser - INFO - Loaded 0 symbols from NSE_CM.csv for market types: ['INDEX']
universal_symbol_parser - INFO - Performance stats: Processed 8288 rows, found 0 valid symbols
universal_symbol_parser - INFO - Found 0 INDEX symbols for scanning
market_type_scanner - WARNING - No INDEX symbols found for pivot point scanning
unified_scanner - WARNING - No INDEX symbols found matching the criteria
unified_scanner - INFO - ============================================================
unified_scanner - INFO - STARTING FUTURES SCANNING
unified_scanner - INFO - ============================================================
universal_symbol_parser - INFO - Universal symbol parser initialized for 2 target symbols
universal_symbol_parser - INFO - Use all symbols: False
market_type_scanner - INFO - Initialized FUTURES scanner
market_type_scanner - INFO - Starting FUTURES scanning...
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
pivot_point_integration - INFO - Pivot Point Integration initialized - Enabled: True, Type: DAILY
universal_symbol_parser - INFO - Loaded 6 symbols from NSE_FO.csv for market types: ['FUTURES']
universal_symbol_parser - INFO - Performance stats: Processed 78328 rows, found 6 valid symbols
universal_symbol_parser - INFO - Found 6 FUTURES symbols for scanning
market_type_scanner - INFO - Pivot point mode: Fetching market data for 6 FUTURES symbols
fyers_client - INFO - Batch 1/1 (6 symbols) - Rate: 0.0 batches/sec, ETA: 0s
fyers_client - INFO - Quote fetching completed: 6/6 symbols (100.0% batch success rate) in 0.3s
market_type_scanner - INFO - Volume filter: 6/6 symbols passed
market_type_scanner - INFO - LTP filter: 6/6 symbols passed
market_type_scanner - INFO - Applying pivot point calculations to 6 symbols
fyers_connect - INFO - Starting Fyers authentication process...
fyers_config - INFO - Fyers authentication successful!
fyers_connect - INFO - Successfully logged in to Fyers API
fyers_connect - INFO - Spot price for DIXON: 16107.0
fyers_connect - INFO - Spot price for MCX: 8259.5
market_type_scanner - INFO - Calculated pivot points for 6 symbols
pivot_point_integration - INFO - Filtered to top 6 symbols closest to minimum positive pivot points
market_type_scanner - INFO - FUTURES pivot point scanning completed: 6 symbols found
report_generator - INFO - Output directory ready: reports
report_generator - INFO - CSV report created successfully: reports\futures_scan_20250718_004031.csv
report_generator - INFO - Report contains 6 symbols
report_generator - INFO - Summary report created successfully: reports\futures_scan_summary_20250718_004031.txt
report_generator - INFO - Generated FUTURES reports:
report_generator - INFO -   CSV: reports\futures_scan_20250718_004031.csv
report_generator - INFO -   Summary: reports\futures_scan_summary_20250718_004031.txt
unified_scanner - INFO - ============================================================
unified_scanner - INFO - FUTURES SCANNING COMPLETED
unified_scanner - INFO - ============================================================
unified_scanner - INFO - FUTURES symbols found: 6
unified_scanner - INFO - FUTURES CSV Report: reports\futures_scan_20250718_004031.csv
unified_scanner - INFO - FUTURES Summary Report: reports\futures_scan_summary_20250718_004031.txt
unified_scanner - INFO - ============================================================
unified_scanner - INFO - STARTING OPTIONS SCANNING
unified_scanner - INFO - ============================================================
universal_symbol_parser - INFO - Universal symbol parser initialized for 2 target symbols
universal_symbol_parser - INFO - Use all symbols: False
market_type_scanner - INFO - Initialized OPTIONS scanner
options_chain_filter - INFO - Options chain filter initialized with strike level: 20
market_type_scanner - INFO - Starting OPTIONS scanning...
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
pivot_point_integration - INFO - Pivot Point Integration initialized - Enabled: True, Type: DAILY
fyers_connect - INFO - Starting Fyers authentication process...
fyers_config - INFO - Fyers authentication successful!
fyers_connect - INFO - Successfully logged in to Fyers API
fyers_connect - INFO - Spot price for DIXON: 16107.0
fyers_connect - INFO - Spot price for MCX: 8259.5
market_type_scanner - INFO - Pivot point mode: Processing symbols: ['DIXON', 'MCX']
root - INFO - Generating config for new symbol 'DIXON' based on spot price.
fyers_connect - ERROR - Monthly option symbol NSE:DIXON25JUL15100CE is invalid or returned no data. Tried symbol: NSE:DIXON25JUL15100CE for expiry_type: MONTHLY. If already using MONTHLY, this is likely a symbol or broker issue.
market_type_scanner - ERROR - Error processing symbol DIXON in pivot point mode: Monthly option symbol is invalid or returned no data. Tried symbol: NSE:DIXON25JUL15100CE for expiry_type: MONTHLY. If already using MONTHLY, this is likely a symbol or broker issue.
root - INFO - Generating config for new symbol 'MCX' based on spot price.
option_utils - WARNING - Could not find implied volatility for symbol=NSE:MCX25JUL7300CE, price=302.45, S=8259.5, K=7300.0, T=0.03561643835616438, r=0.065, option_type=CE: f(a) and f(b) must have different signs. Returning default IV.
market_type_scanner - INFO - Pivot point mode: Added 11 options for MCX
market_type_scanner - INFO - Pivot point mode: Total symbols selected: 11
market_type_scanner - INFO - Pivot point mode: Fetching market data for 11 OPTIONS symbols
fyers_client - INFO - Batch 1/1 (11 symbols) - Rate: 0.0 batches/sec, ETA: 0s
fyers_client - INFO - Quote fetching completed: 11/11 symbols (100.0% batch success rate) in 0.2s
market_type_scanner - INFO - Volume filter: 11/11 symbols passed
market_type_scanner - INFO - LTP filter: 11/11 symbols passed
market_type_scanner - INFO - Applying pivot point calculations to 11 symbols
fyers_connect - INFO - Starting Fyers authentication process...
fyers_config - INFO - Fyers authentication successful!
fyers_connect - INFO - Successfully logged in to Fyers API
fyers_connect - INFO - Spot price for DIXON: 16107.0
fyers_connect - INFO - Spot price for MCX: 8259.5
market_type_scanner - INFO - Calculated pivot points for 11 symbols
pivot_point_integration - INFO - Filtered to top 10 symbols closest to minimum positive pivot points
market_type_scanner - INFO - OPTIONS pivot point scanning completed: 10 symbols found
report_generator - INFO - Output directory ready: reports
report_generator - WARNING - Found 10 unpaired symbols
report_generator - INFO - Sorted 10 symbols by underlying, month, and preserved CE/PE pairing
report_generator - INFO - Preserved 0 CE/PE pairs, 10 unpaired symbols
options_chain_filter - INFO - Options chain filter initialized with strike level: 20
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
options_chain_filter - INFO - Fetching spot prices for 1 underlyings...
fyers_client - INFO - Batch 1/1 (1 symbols) - Rate: 0.0 batches/sec, ETA: 0s
fyers_client - INFO - Quote fetching completed: 1/1 symbols (100.0% batch success rate) in 0.2s
options_chain_filter - INFO - Retrieved spot prices for 1/1 underlyings
report_generator - INFO - Fetched spot prices for 1 underlyings for CSV report
report_generator - INFO - CSV report created successfully: reports\options_scan_20250718_004036.csv
report_generator - INFO - Report contains 10 symbols
report_generator - WARNING - Found 10 unpaired symbols
report_generator - INFO - Sorted 10 symbols by underlying, month, and preserved CE/PE pairing
report_generator - INFO - Preserved 0 CE/PE pairs, 10 unpaired symbols
report_generator - INFO - Summary report created successfully: reports\options_scan_summary_20250718_004036.txt
report_generator - INFO - Generated OPTIONS reports:
report_generator - INFO -   CSV: reports\options_scan_20250718_004036.csv
report_generator - INFO -   Summary: reports\options_scan_summary_20250718_004036.txt
unified_scanner - INFO - ============================================================
unified_scanner - INFO - OPTIONS SCANNING COMPLETED
unified_scanner - INFO - ============================================================
unified_scanner - INFO - OPTIONS symbols found: 10
unified_scanner - INFO - OPTIONS CSV Report: reports\options_scan_20250718_004036.csv
unified_scanner - INFO - OPTIONS Summary Report: reports\options_scan_summary_20250718_004036.txt
unified_scanner - INFO - Combined summary report generated: reports\combined_scan_summary_20250718_004036.txt
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - UNIFIED SCANNER COMPLETED SUCCESSFULLY
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - End time: 2025-07-18 00:40:37
unified_scanner - INFO - Total symbols found: 18
unified_scanner - INFO -   - EQUITY symbols: 2
unified_scanner - INFO -   - INDEX symbols: 0
unified_scanner - INFO -   - FUTURES symbols: 6
unified_scanner - INFO -   - OPTIONS symbols: 10
unified_scanner - INFO - Combined summary: reports\combined_scan_summary_20250718_004036.txt
root - INFO - ================================================================================
root - INFO - Starting new logging session
root - INFO - ================================================================================
fyers_config - INFO - ================================================================================
fyers_config - INFO - UNIFIED SCANNER APPLICATION STARTED
fyers_config - INFO - ================================================================================
fyers_config - INFO - Start time: 2025-07-18 15:53:50
config_loader - INFO - Configuration loaded successfully from C:\Users\<USER>\Desktop\Python\simple_hft\config.yaml
unified_scanner - INFO - Unified scanner initialized for market types: ['EQUITY', 'INDEX', 'FUTURES', 'OPTIONS']
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - UNIFIED SCANNER APPLICATION STARTED
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - Start time: 2025-07-18 15:53:50
unified_scanner - INFO - Enabled market types: EQUITY, INDEX, FUTURES, OPTIONS
unified_scanner - INFO - Validating prerequisites...
config_loader - INFO - Configuration validation successful
unified_scanner - INFO - All prerequisites satisfied
unified_scanner - INFO - Downloading latest symbol files...
unified_scanner - INFO - Symbol files downloaded successfully
unified_scanner - INFO - ============================================================
unified_scanner - INFO - STARTING EQUITY SCANNING
unified_scanner - INFO - ============================================================
universal_symbol_parser - INFO - Universal symbol parser initialized for 2 target symbols
universal_symbol_parser - INFO - Use all symbols: False
market_type_scanner - INFO - Initialized EQUITY scanner
market_type_scanner - INFO - Starting EQUITY scanning...
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
pivot_point_integration - INFO - Pivot Point Integration initialized - Enabled: True, Type: DAILY
universal_symbol_parser - INFO - Loaded 2 symbols from NSE_CM.csv for market types: ['EQUITY']
universal_symbol_parser - INFO - Performance stats: Processed 8313 rows, found 2 valid symbols
universal_symbol_parser - INFO - Found 2 EQUITY symbols for scanning
market_type_scanner - INFO - Pivot point mode: Fetching market data for 2 EQUITY symbols
fyers_client - INFO - Batch 1/1 (2 symbols) - Rate: 0.0 batches/sec, ETA: 0s
fyers_client - INFO - Quote fetching completed: 2/2 symbols (100.0% batch success rate) in 0.3s
market_type_scanner - INFO - Volume filter: 2/2 symbols passed
market_type_scanner - INFO - LTP filter: 2/2 symbols passed
market_type_scanner - INFO - Applying pivot point calculations to 2 symbols
fyers_connect - INFO - Starting Fyers authentication process...
fyers_config - INFO - Fyers authentication successful!
fyers_connect - INFO - Successfully logged in to Fyers API
fyers_connect - INFO - Spot price for DIXON: 15960.0
fyers_connect - INFO - Spot price for MCX: 8235.0
market_type_scanner - INFO - Calculated pivot points for 2 symbols
pivot_point_integration - INFO - Filtered to top 2 symbols closest to minimum positive pivot points
market_type_scanner - INFO - EQUITY pivot point scanning completed: 2 symbols found
report_generator - INFO - Output directory ready: reports
report_generator - INFO - CSV report created successfully: reports\equity_scan_20250718_155357.csv
report_generator - INFO - Report contains 2 symbols
report_generator - INFO - Summary report created successfully: reports\equity_scan_summary_20250718_155357.txt
report_generator - INFO - Generated EQUITY reports:
report_generator - INFO -   CSV: reports\equity_scan_20250718_155357.csv
report_generator - INFO -   Summary: reports\equity_scan_summary_20250718_155357.txt
unified_scanner - INFO - ============================================================
unified_scanner - INFO - EQUITY SCANNING COMPLETED
unified_scanner - INFO - ============================================================
unified_scanner - INFO - EQUITY symbols found: 2
unified_scanner - INFO - EQUITY CSV Report: reports\equity_scan_20250718_155357.csv
unified_scanner - INFO - EQUITY Summary Report: reports\equity_scan_summary_20250718_155357.txt
unified_scanner - INFO - ============================================================
unified_scanner - INFO - STARTING INDEX SCANNING
unified_scanner - INFO - ============================================================
universal_symbol_parser - INFO - Universal symbol parser initialized for 2 target symbols
universal_symbol_parser - INFO - Use all symbols: False
market_type_scanner - INFO - Initialized INDEX scanner
market_type_scanner - INFO - Starting INDEX scanning...
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
pivot_point_integration - INFO - Pivot Point Integration initialized - Enabled: True, Type: DAILY
universal_symbol_parser - INFO - Loaded 0 symbols from NSE_CM.csv for market types: ['INDEX']
universal_symbol_parser - INFO - Performance stats: Processed 8313 rows, found 0 valid symbols
universal_symbol_parser - INFO - Found 0 INDEX symbols for scanning
market_type_scanner - WARNING - No INDEX symbols found for pivot point scanning
unified_scanner - WARNING - No INDEX symbols found matching the criteria
unified_scanner - INFO - ============================================================
unified_scanner - INFO - STARTING FUTURES SCANNING
unified_scanner - INFO - ============================================================
universal_symbol_parser - INFO - Universal symbol parser initialized for 2 target symbols
universal_symbol_parser - INFO - Use all symbols: False
market_type_scanner - INFO - Initialized FUTURES scanner
market_type_scanner - INFO - Starting FUTURES scanning...
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
pivot_point_integration - INFO - Pivot Point Integration initialized - Enabled: True, Type: DAILY
universal_symbol_parser - INFO - Loaded 6 symbols from NSE_FO.csv for market types: ['FUTURES']
universal_symbol_parser - INFO - Performance stats: Processed 78482 rows, found 6 valid symbols
universal_symbol_parser - INFO - Found 6 FUTURES symbols for scanning
market_type_scanner - INFO - Pivot point mode: Fetching market data for 6 FUTURES symbols
fyers_client - INFO - Batch 1/1 (6 symbols) - Rate: 0.0 batches/sec, ETA: 0s
fyers_client - INFO - Quote fetching completed: 6/6 symbols (100.0% batch success rate) in 0.2s
market_type_scanner - INFO - Volume filter: 6/6 symbols passed
market_type_scanner - INFO - LTP filter: 6/6 symbols passed
market_type_scanner - INFO - Applying pivot point calculations to 6 symbols
fyers_connect - INFO - Starting Fyers authentication process...
fyers_config - INFO - Fyers authentication successful!
fyers_connect - INFO - Successfully logged in to Fyers API
fyers_connect - INFO - Spot price for DIXON: 15960.0
fyers_connect - INFO - Spot price for MCX: 8235.0
market_type_scanner - INFO - Calculated pivot points for 6 symbols
pivot_point_integration - INFO - Filtered to top 6 symbols closest to minimum positive pivot points
market_type_scanner - INFO - FUTURES pivot point scanning completed: 6 symbols found
report_generator - INFO - Output directory ready: reports
report_generator - INFO - CSV report created successfully: reports\futures_scan_20250718_155404.csv
report_generator - INFO - Report contains 6 symbols
report_generator - INFO - Summary report created successfully: reports\futures_scan_summary_20250718_155404.txt
report_generator - INFO - Generated FUTURES reports:
report_generator - INFO -   CSV: reports\futures_scan_20250718_155404.csv
report_generator - INFO -   Summary: reports\futures_scan_summary_20250718_155404.txt
unified_scanner - INFO - ============================================================
unified_scanner - INFO - FUTURES SCANNING COMPLETED
unified_scanner - INFO - ============================================================
unified_scanner - INFO - FUTURES symbols found: 6
unified_scanner - INFO - FUTURES CSV Report: reports\futures_scan_20250718_155404.csv
unified_scanner - INFO - FUTURES Summary Report: reports\futures_scan_summary_20250718_155404.txt
unified_scanner - INFO - ============================================================
unified_scanner - INFO - STARTING OPTIONS SCANNING
unified_scanner - INFO - ============================================================
universal_symbol_parser - INFO - Universal symbol parser initialized for 2 target symbols
universal_symbol_parser - INFO - Use all symbols: False
market_type_scanner - INFO - Initialized OPTIONS scanner
options_chain_filter - INFO - Options chain filter initialized with strike level: 20
market_type_scanner - INFO - Starting OPTIONS scanning...
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
pivot_point_integration - INFO - Pivot Point Integration initialized - Enabled: True, Type: DAILY
fyers_connect - INFO - Starting Fyers authentication process...
fyers_config - INFO - Fyers authentication successful!
fyers_connect - INFO - Successfully logged in to Fyers API
fyers_connect - INFO - Spot price for DIXON: 15960.0
fyers_connect - INFO - Spot price for MCX: 8235.0
market_type_scanner - INFO - Pivot point mode: Processing symbols: ['DIXON', 'MCX']
root - INFO - Generating config for new symbol 'DIXON' based on spot price.
fyers_connect - ERROR - Monthly option symbol NSE:DIXON25JUL15100CE is invalid or returned no data. Tried symbol: NSE:DIXON25JUL15100CE for expiry_type: MONTHLY. If already using MONTHLY, this is likely a symbol or broker issue.
market_type_scanner - ERROR - Error processing symbol DIXON in pivot point mode: Monthly option symbol is invalid or returned no data. Tried symbol: NSE:DIXON25JUL15100CE for expiry_type: MONTHLY. If already using MONTHLY, this is likely a symbol or broker issue.
root - INFO - Generating config for new symbol 'MCX' based on spot price.
option_utils - WARNING - Could not find implied volatility for symbol=NSE:MCX25JUL7300CE, price=302.45, S=8235.0, K=7300.0, T=0.03561643835616438, r=0.065, option_type=CE: f(a) and f(b) must have different signs. Returning default IV.
option_utils - WARNING - Could not find implied volatility for symbol=NSE:MCX25JUL7600CE, price=604.9, S=8235.0, K=7600.0, T=0.03561643835616438, r=0.065, option_type=CE: f(a) and f(b) must have different signs. Returning default IV.
option_utils - WARNING - Could not find implied volatility for symbol=NSE:MCX25JUL7700CE, price=491.1, S=8235.0, K=7700.0, T=0.03561643835616438, r=0.065, option_type=CE: f(a) and f(b) must have different signs. Returning default IV.
market_type_scanner - INFO - Pivot point mode: Added 9 options for MCX
market_type_scanner - INFO - Pivot point mode: Total symbols selected: 9
market_type_scanner - INFO - Pivot point mode: Fetching market data for 9 OPTIONS symbols
fyers_client - INFO - Batch 1/1 (9 symbols) - Rate: 0.0 batches/sec, ETA: 0s
fyers_client - INFO - Quote fetching completed: 9/9 symbols (100.0% batch success rate) in 0.3s
market_type_scanner - INFO - Volume filter: 9/9 symbols passed
market_type_scanner - INFO - LTP filter: 9/9 symbols passed
market_type_scanner - INFO - Applying pivot point calculations to 9 symbols
fyers_connect - INFO - Starting Fyers authentication process...
fyers_config - INFO - Fyers authentication successful!
fyers_connect - INFO - Successfully logged in to Fyers API
fyers_connect - INFO - Spot price for DIXON: 15960.0
fyers_connect - INFO - Spot price for MCX: 8235.0
market_type_scanner - INFO - Calculated pivot points for 9 symbols
pivot_point_integration - INFO - Filtered to top 9 symbols closest to minimum positive pivot points
market_type_scanner - INFO - OPTIONS pivot point scanning completed: 9 symbols found
report_generator - INFO - Output directory ready: reports
report_generator - WARNING - Found 9 unpaired symbols
report_generator - INFO - Sorted 9 symbols by underlying, month, and preserved CE/PE pairing
report_generator - INFO - Preserved 0 CE/PE pairs, 9 unpaired symbols
options_chain_filter - INFO - Options chain filter initialized with strike level: 20
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
options_chain_filter - INFO - Fetching spot prices for 1 underlyings...
fyers_client - INFO - Batch 1/1 (1 symbols) - Rate: 0.0 batches/sec, ETA: 0s
fyers_client - INFO - Quote fetching completed: 1/1 symbols (100.0% batch success rate) in 0.2s
options_chain_filter - INFO - Retrieved spot prices for 1/1 underlyings
report_generator - INFO - Fetched spot prices for 1 underlyings for CSV report
report_generator - INFO - CSV report created successfully: reports\options_scan_20250718_155412.csv
report_generator - INFO - Report contains 9 symbols
report_generator - WARNING - Found 9 unpaired symbols
report_generator - INFO - Sorted 9 symbols by underlying, month, and preserved CE/PE pairing
report_generator - INFO - Preserved 0 CE/PE pairs, 9 unpaired symbols
report_generator - INFO - Summary report created successfully: reports\options_scan_summary_20250718_155412.txt
report_generator - INFO - Generated OPTIONS reports:
report_generator - INFO -   CSV: reports\options_scan_20250718_155412.csv
report_generator - INFO -   Summary: reports\options_scan_summary_20250718_155412.txt
unified_scanner - INFO - ============================================================
unified_scanner - INFO - OPTIONS SCANNING COMPLETED
unified_scanner - INFO - ============================================================
unified_scanner - INFO - OPTIONS symbols found: 9
unified_scanner - INFO - OPTIONS CSV Report: reports\options_scan_20250718_155412.csv
unified_scanner - INFO - OPTIONS Summary Report: reports\options_scan_summary_20250718_155412.txt
unified_scanner - INFO - Combined summary report generated: reports\combined_scan_summary_20250718_155412.txt
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - UNIFIED SCANNER COMPLETED SUCCESSFULLY
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - End time: 2025-07-18 15:54:12
unified_scanner - INFO - Total symbols found: 17
unified_scanner - INFO -   - EQUITY symbols: 2
unified_scanner - INFO -   - INDEX symbols: 0
unified_scanner - INFO -   - FUTURES symbols: 6
unified_scanner - INFO -   - OPTIONS symbols: 9
unified_scanner - INFO - Combined summary: reports\combined_scan_summary_20250718_155412.txt
root - INFO - ================================================================================
root - INFO - Starting new logging session
root - INFO - ================================================================================
fyers_config - INFO - ================================================================================
fyers_config - INFO - UNIFIED SCANNER APPLICATION STARTED
fyers_config - INFO - ================================================================================
fyers_config - INFO - Start time: 2025-07-18 15:58:43
config_loader - INFO - Configuration loaded successfully from C:\Users\<USER>\Desktop\Python\simple_hft\config.yaml
unified_scanner - INFO - Unified scanner initialized for market types: ['OPTIONS']
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - UNIFIED SCANNER APPLICATION STARTED
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - Start time: 2025-07-18 15:58:43
unified_scanner - INFO - Enabled market types: OPTIONS
unified_scanner - INFO - Validating prerequisites...
config_loader - INFO - Configuration validation successful
unified_scanner - INFO - All prerequisites satisfied
unified_scanner - INFO - Downloading latest symbol files...
unified_scanner - INFO - Symbol files downloaded successfully
unified_scanner - INFO - ============================================================
unified_scanner - INFO - STARTING OPTIONS SCANNING
unified_scanner - INFO - ============================================================
universal_symbol_parser - INFO - Universal symbol parser initialized for 1 target symbols
universal_symbol_parser - INFO - Use all symbols: True
market_type_scanner - INFO - Initialized OPTIONS scanner
options_chain_filter - INFO - Options chain filter initialized with strike level: 20
market_type_scanner - INFO - Starting OPTIONS scanning...
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
pivot_point_integration - INFO - Pivot Point Integration initialized - Enabled: True, Type: DAILY
fyers_connect - INFO - Starting Fyers authentication process...
fyers_config - INFO - Fyers authentication successful!
fyers_connect - INFO - Successfully logged in to Fyers API
fyers_connect - WARNING - Attempt 1 to fetch quote for ALL failed. Response: {'message': '', 'code': 200, 'd': [{'n': 'NSE:ALL-EQ', 'v': {'n': 'NSE:ALL-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}], 's': 'ok'}
fyers_connect - WARNING - Attempt 2 to fetch quote for ALL failed. Response: {'message': '', 'code': 200, 'd': [{'n': 'NSE:ALL-EQ', 'v': {'n': 'NSE:ALL-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}], 's': 'ok'}
fyers_connect - WARNING - Attempt 3 to fetch quote for ALL failed. Response: {'message': '', 'code': 200, 'd': [{'n': 'NSE:ALL-EQ', 'v': {'n': 'NSE:ALL-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}], 's': 'ok'}
fyers_connect - INFO - Spot price for ALL: 0.0
market_type_scanner - INFO - Pivot point mode: Processing symbols: ['ALL']
fyers_connect - WARNING - Attempt 1 to fetch quote for ALL failed. Response: {'message': '', 'code': 200, 'd': [{'n': 'NSE:ALL-EQ', 'v': {'n': 'NSE:ALL-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}], 's': 'ok'}
fyers_connect - WARNING - Attempt 2 to fetch quote for ALL failed. Response: {'message': '', 'code': 200, 'd': [{'n': 'NSE:ALL-EQ', 'v': {'n': 'NSE:ALL-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}], 's': 'ok'}
fyers_connect - WARNING - Attempt 3 to fetch quote for ALL failed. Response: {'message': '', 'code': 200, 'd': [{'n': 'NSE:ALL-EQ', 'v': {'n': 'NSE:ALL-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}], 's': 'ok'}
unified_scanner - INFO - Application interrupted by user
root - INFO - ================================================================================
root - INFO - Starting new logging session
root - INFO - ================================================================================
fyers_config - INFO - ================================================================================
fyers_config - INFO - UNIFIED SCANNER APPLICATION STARTED
fyers_config - INFO - ================================================================================
fyers_config - INFO - Start time: 2025-07-18 16:03:30
config_loader - INFO - Configuration loaded successfully from C:\Users\<USER>\Desktop\Python\simple_hft\config.yaml
unified_scanner - INFO - Unified scanner initialized for market types: ['OPTIONS']
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - UNIFIED SCANNER APPLICATION STARTED
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - Start time: 2025-07-18 16:03:30
unified_scanner - INFO - Enabled market types: OPTIONS
unified_scanner - INFO - Validating prerequisites...
config_loader - INFO - Configuration validation successful
unified_scanner - INFO - All prerequisites satisfied
unified_scanner - INFO - Downloading latest symbol files...
unified_scanner - INFO - Symbol files downloaded successfully
unified_scanner - INFO - ============================================================
unified_scanner - INFO - STARTING OPTIONS SCANNING
unified_scanner - INFO - ============================================================
universal_symbol_parser - INFO - Universal symbol parser initialized for 1 target symbols
universal_symbol_parser - INFO - Use all symbols: True
market_type_scanner - INFO - Initialized OPTIONS scanner
options_chain_filter - INFO - Options chain filter initialized with strike level: 20
market_type_scanner - INFO - Starting OPTIONS scanning...
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
pivot_point_integration - INFO - Pivot Point Integration initialized - Enabled: True, Type: DAILY
fyers_connect - INFO - Starting Fyers authentication process...
fyers_config - INFO - Fyers authentication successful!
fyers_connect - INFO - Successfully logged in to Fyers API
fyers_connect - INFO - Symbol 'ALL' is a placeholder for all symbols - skipping spot price fetch
market_type_scanner - INFO - Pivot point mode: Processing symbols: ['ALL']
fyers_connect - WARNING - Attempt 1 to fetch quote for ALL failed. Response: {'message': '', 'code': 200, 'd': [{'n': 'NSE:ALL-EQ', 'v': {'n': 'NSE:ALL-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}], 's': 'ok'}
fyers_connect - WARNING - Attempt 2 to fetch quote for ALL failed. Response: {'message': '', 'code': 200, 'd': [{'n': 'NSE:ALL-EQ', 'v': {'n': 'NSE:ALL-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}], 's': 'ok'}
fyers_connect - WARNING - Attempt 3 to fetch quote for ALL failed. Response: {'message': '', 'code': 200, 'd': [{'n': 'NSE:ALL-EQ', 'v': {'n': 'NSE:ALL-EQ', 'errmsg': 'Please provide a valid symbol', 'code': -300, 's': 'error'}, 's': 'ok'}], 's': 'ok'}
fyers_connect - ERROR - Error fetching option chain: Failed to fetch spot price for ALL after multiple retries.
market_type_scanner - WARNING - No option chain data fetched for ALL
market_type_scanner - INFO - Pivot point mode: Total symbols selected: 0
market_type_scanner - WARNING - No OPTIONS symbols found for pivot point scanning
unified_scanner - WARNING - No OPTIONS symbols found matching the criteria
unified_scanner - INFO - Combined summary report generated: reports\combined_scan_summary_20250718_160336.txt
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - UNIFIED SCANNER COMPLETED SUCCESSFULLY
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - End time: 2025-07-18 16:03:36
unified_scanner - INFO - Total symbols found: 0
unified_scanner - INFO -   - OPTIONS symbols: 0
unified_scanner - INFO - Combined summary: reports\combined_scan_summary_20250718_160336.txt
